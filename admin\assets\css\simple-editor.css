/**
 * SimpleEditor - CSS
 * Phát triển bởi: <PERSON><PERSON><PERSON> thất <PERSON>ng <PERSON>
 * Version: 1.0.0
 */

/* Container */
.simple-editor {
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Toolbar */
.simple-editor-toolbar {
    display: flex;
    flex-wrap: wrap;
    padding: 8px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
    gap: 4px;
}

/* Buttons */
.simple-editor-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    padding: 0;
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    color: #495057;
    transition: all 0.2s ease;
}

.simple-editor-button:hover {
    background-color: #e9ecef;
    border-color: #ced4da;
}

.simple-editor-button:active,
.simple-editor-button.active {
    background-color: #e2e6ea;
    border-color: #dae0e5;
}

.simple-editor-button:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Color Picker */
.simple-editor-color-picker {
    position: relative;
    display: inline-block;
}

.simple-editor-color-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    min-width: 200px;
    padding: 10px;
    margin: 2px 0 0;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
}

.simple-editor-color-dropdown.show {
    display: block;
}

.simple-editor-color-input {
    width: 100%;
    height: 36px;
    cursor: pointer;
    margin-top: 5px;
}

.simple-editor-recent-colors {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 10px;
}

.simple-editor-color-item {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    border: 1px solid #ddd;
    cursor: pointer;
    transition: transform 0.2s;
}

.simple-editor-color-item:hover {
    transform: scale(1.1);
    border-color: #aaa;
}

.simple-editor-color-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

/* Dropdown */
.simple-editor-dropdown {
    position: relative;
    display: inline-block;
}

.simple-editor-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    max-height: 300px;
    overflow-y: auto;
}

.simple-editor-dropdown-menu.show {
    display: block;
}

.simple-editor-dropdown-item {
    display: block;
    padding: 6px 12px;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: inherit;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
    text-decoration: none;
    cursor: pointer;
}

.simple-editor-dropdown-item:hover {
    color: #16181b;
    text-decoration: none;
    background-color: #f8f9fa;
}

/* Định dạng cho các kích thước font */
.simple-editor-content font[size="1"] {
    font-size: 8pt;
}

.simple-editor-content font[size="2"] {
    font-size: 10pt;
}

.simple-editor-content font[size="3"] {
    font-size: 12pt;
}

.simple-editor-content font[size="4"] {
    font-size: 14pt;
}

.simple-editor-content font[size="5"] {
    font-size: 18pt;
}

.simple-editor-content font[size="6"] {
    font-size: 24pt;
}

.simple-editor-content font[size="7"] {
    font-size: 36pt;
}

/* Content Area */
.simple-editor-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    min-height: 200px;
    line-height: 1.5;
    color: #212529;
}

.simple-editor-content:focus {
    outline: none;
}

.simple-editor-content[placeholder]:empty:before {
    content: attr(placeholder);
    color: #adb5bd;
    pointer-events: none;
}

/* Source Mode */
.simple-editor-source {
    flex: 1;
    padding: 16px;
    font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    color: #212529;
    background-color: #f8f9fa;
    border: none;
    resize: none;
    min-height: 200px;
}

.simple-editor-source:focus {
    outline: none;
}

/* Notifications */
.simple-editor-notification {
    position: absolute;
    bottom: 16px;
    right: 16px;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    color: #fff;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.simple-editor-notification-info {
    background-color: #17a2b8;
}

.simple-editor-notification-success {
    background-color: #28a745;
}

.simple-editor-notification-error {
    background-color: #dc3545;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Figures and Images */
.simple-editor-image-container {
    display: block;
    margin: 1em 0;
    max-width: 100%;
}

.simple-editor-figure {
    display: table;
    margin: 0 auto;
    max-width: 100%;
    text-align: center;
}

.simple-editor-image {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
}

.simple-editor-caption {
    display: table-caption;
    caption-side: bottom;
    padding: 8px;
    font-size: 14px;
    color: #6c757d;
    text-align: center;
    font-style: italic;
}

/* Tables */
.simple-editor-table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    border-collapse: collapse;
}

.simple-editor-table th,
.simple-editor-table td {
    padding: 8px;
    border: 1px solid #dee2e6;
}

.simple-editor-table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
    background-color: #f8f9fa;
}

.simple-editor-table tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Responsive Design */
@media (max-width: 768px) {
    .simple-editor-toolbar {
        flex-wrap: wrap;
    }

    .simple-editor-button {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }
}

/* Định dạng nội dung trong editor */
.simple-editor-content h1 {
    font-size: 2em;
    margin-top: 0.67em;
    margin-bottom: 0.67em;
}

.simple-editor-content h2 {
    font-size: 1.5em;
    margin-top: 0.83em;
    margin-bottom: 0.83em;
}

.simple-editor-content h3 {
    font-size: 1.17em;
    margin-top: 1em;
    margin-bottom: 1em;
}

.simple-editor-content h4 {
    font-size: 1em;
    margin-top: 1.33em;
    margin-bottom: 1.33em;
}

.simple-editor-content h5 {
    font-size: 0.83em;
    margin-top: 1.67em;
    margin-bottom: 1.67em;
}

.simple-editor-content h6 {
    font-size: 0.67em;
    margin-top: 2.33em;
    margin-bottom: 2.33em;
}

.simple-editor-content p {
    margin-top: 1em;
    margin-bottom: 1em;
}

.simple-editor-content ul,
.simple-editor-content ol {
    margin-top: 1em;
    margin-bottom: 1em;
    padding-left: 40px;
}

.simple-editor-content blockquote {
    margin: 1em 40px;
    padding: 10px 20px;
    border-left: 5px solid #ced4da;
    background-color: #f8f9fa;
}

.simple-editor-content a {
    color: #007bff;
    text-decoration: underline;
}

.simple-editor-content a:hover {
    color: #0056b3;
    text-decoration: underline;
}

.simple-editor-content img {
    max-width: 100%;
    height: auto;
}

/* Định dạng căn chỉnh */
.simple-editor-content .text-left {
    text-align: left;
}

.simple-editor-content .text-center {
    text-align: center;
}

.simple-editor-content .text-right {
    text-align: right;
}

.simple-editor-content .text-justify {
    text-align: justify;
}