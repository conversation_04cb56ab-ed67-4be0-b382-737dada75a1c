/* Summernote Custom CSS */

/* <PERSON><PERSON><PERSON> bảo trình soạn thảo hiển thị đúng */
.note-editor.note-frame {
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.note-editor.note-frame .note-editing-area {
    background-color: #fff;
}

.note-editor.note-frame .note-statusbar {
    background-color: #f8f9fa;
}

.note-editor.note-frame .note-toolbar {
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
    padding: 8px;
}

/* <PERSON><PERSON><PERSON> b<PERSON><PERSON> các nút hiển thị đúng */
.note-btn {
    border-color: #ddd;
    background-color: #fff;
    padding: 5px 10px;
}

.note-btn:hover {
    background-color: #f0f0f0;
}

.note-btn.active {
    background-color: #e6e6e6;
}

/* <PERSON><PERSON><PERSON> b<PERSON><PERSON> dropdown hiển thị đúng */
.note-dropdown-menu {
    min-width: 160px;
    padding: 5px 0;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
}

.note-dropdown-item {
    display: block;
    padding: 3px 20px;
    clear: both;
    font-weight: 400;
    line-height: 1.5;
    color: #333;
    white-space: nowrap;
}

.note-dropdown-item:hover {
    background-color: #f5f5f5;
    color: #262626;
    text-decoration: none;
}

/* Đảm bảo modal hiển thị đúng */
.note-modal-title {
    font-size: 18px;
    margin: 0;
    line-height: 1.5;
}

.note-modal-header {
    padding: 15px;
    border-bottom: 1px solid #e5e5e5;
}

.note-modal-body {
    position: relative;
    padding: 15px;
}

.note-modal-footer {
    padding: 15px;
    text-align: right;
    border-top: 1px solid #e5e5e5;
}

/* Đảm bảo nội dung soạn thảo hiển thị đúng */
.note-editable {
    background-color: #fff;
    color: #000;
    padding: 10px;
    overflow: auto;
    outline: none;
    min-height: 300px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    line-height: 1.6;
}

.note-editable p {
    margin-bottom: 1em;
}

.note-editable h1, .note-editable h2, .note-editable h3,
.note-editable h4, .note-editable h5, .note-editable h6 {
    margin-top: 1em;
    margin-bottom: 0.5em;
}

/* Đảm bảo hiển thị đúng trên thiết bị di động */
@media (max-width: 768px) {
    .note-editor.note-frame .note-toolbar {
        padding: 5px;
    }
    
    .note-btn {
        padding: 3px 6px;
        font-size: 12px;
    }
    
    .note-editable {
        min-height: 200px;
    }
}

/* Ghi đè các kiểu CSS khác có thể gây xung đột */
.note-editor.note-frame * {
    box-sizing: border-box;
}

/* Đảm bảo z-index cho dropdown và modal */
.note-dropdown-menu {
    z-index: 1050;
}

.note-modal {
    z-index: 1060;
}

/* Đảm bảo hiển thị đúng khi focus */
.note-editor.note-frame.focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
