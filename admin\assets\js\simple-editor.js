/**
 * SimpleEditor - Trình soạn thảo văn bản đơn giản cho website nội thất
 * Phát triển bởi: Nội thất Bàng Vũ
 * Version: 1.0.0
 */

class SimpleEditor {
  constructor(options = {}) {
    // Các tùy chọn mặc định
    this.defaults = {
      element: null, // Element để khởi tạo editor
      placeholder: 'Nhập nội dung...', // Placeholder text
      uploadUrl: 'upload-image.php', // URL xử lý upload ảnh
      height: '400px', // Chiều cao của editor
      value: '', // Giá trị ban đầu
      onChange: null, // Callback khi nội dung thay đổi
      buttons: [
        'bold',
        'italic',
        'underline',
        'strikethrough',
        'fontColor',
        'heading',
        'paragraph',
        'alignLeft',
        'alignCenter',
        'alignRight',
        'alignJustify',
        'bulletList',
        'numberedList',
        'link',
        'image',
        'table',
        'undo',
        'redo',
        'clear',
        'source',
      ],
    };

    // Gộp tùy chọn người dùng với mặc định
    this.options = Object.assign({}, this.defaults, options);

    // Kiểm tra element
    if (!this.options.element) {
      console.error('SimpleEditor: Không tìm thấy element để khởi tạo editor');
      return;
    }

    // Khởi tạo editor
    this.init();
  }

  /**
   * Khởi tạo editor
   */
  init() {
    // Tạo container cho editor
    this.createEditorContainer();

    // Tạo thanh công cụ
    this.createToolbar();

    // Tạo khu vực soạn thảo
    this.createEditorArea();

    // Khởi tạo sự kiện
    this.initEvents();

    // Thiết lập giá trị ban đầu
    if (this.options.value) {
      this.setContent(this.options.value);
    }
  }

  /**
   * Tạo container cho editor
   */
  createEditorContainer() {
    // Lấy element gốc
    this.originalElement = this.options.element;

    // Tạo container
    this.container = document.createElement('div');
    this.container.className = 'simple-editor';
    this.container.style.height = this.options.height;

    // Chèn container vào sau element gốc
    this.originalElement.parentNode.insertBefore(
      this.container,
      this.originalElement.nextSibling
    );

    // Ẩn element gốc
    this.originalElement.style.display = 'none';
  }

  /**
   * Tạo thanh công cụ
   */
  createToolbar() {
    // Tạo thanh công cụ
    this.toolbar = document.createElement('div');
    this.toolbar.className = 'simple-editor-toolbar';

    // Thêm các nút vào thanh công cụ
    this.options.buttons.forEach((button) => {
      let buttonElement;

      switch (button) {
        case 'bold':
          buttonElement = this.createToolbarButton(
            'bold',
            '<i class="fas fa-bold"></i>',
            'Đậm (Ctrl+B)'
          );
          break;
        case 'italic':
          buttonElement = this.createToolbarButton(
            'italic',
            '<i class="fas fa-italic"></i>',
            'Nghiêng (Ctrl+I)'
          );
          break;
        case 'underline':
          buttonElement = this.createToolbarButton(
            'underline',
            '<i class="fas fa-underline"></i>',
            'Gạch chân (Ctrl+U)'
          );
          break;
        case 'strikethrough':
          buttonElement = this.createToolbarButton(
            'strikethrough',
            '<i class="fas fa-strikethrough"></i>',
            'Gạch ngang'
          );
          break;
        case 'fontColor':
          buttonElement = this.createColorPicker(
            'foreColor',
            '<i class="fas fa-palette"></i>',
            'Màu chữ'
          );
          break;
        case 'fontSize':
          buttonElement = this.createDropdown(
            'fontSize',
            '<i class="fas fa-text-height"></i>',
            'Kích thước chữ',
            [
              { value: '1', text: 'Rất nhỏ (8pt)' },
              { value: '2', text: 'Nhỏ (10pt)' },
              { value: '3', text: 'Bình thường (12pt)' },
              { value: '4', text: 'Lớn (14pt)' },
              { value: '5', text: 'Rất lớn (18pt)' },
              { value: '6', text: 'Cực lớn (24pt)' },
              { value: '7', text: 'Siêu lớn (36pt)' },
            ]
          );
          break;
        case 'heading':
          buttonElement = this.createDropdown(
            'heading',
            '<i class="fas fa-heading"></i>',
            'Tiêu đề',
            [
              { value: 'h1', text: 'Tiêu đề 1' },
              { value: 'h2', text: 'Tiêu đề 2' },
              { value: 'h3', text: 'Tiêu đề 3' },
              { value: 'h4', text: 'Tiêu đề 4' },
              { value: 'h5', text: 'Tiêu đề 5' },
              { value: 'h6', text: 'Tiêu đề 6' },
            ]
          );
          break;
        case 'paragraph':
          buttonElement = this.createToolbarButton(
            'paragraph',
            '<i class="fas fa-paragraph"></i>',
            'Đoạn văn'
          );
          break;
        case 'alignLeft':
          buttonElement = this.createToolbarButton(
            'justifyLeft',
            '<i class="fas fa-align-left"></i>',
            'Căn trái'
          );
          break;
        case 'alignCenter':
          buttonElement = this.createToolbarButton(
            'justifyCenter',
            '<i class="fas fa-align-center"></i>',
            'Căn giữa'
          );
          break;
        case 'alignRight':
          buttonElement = this.createToolbarButton(
            'justifyRight',
            '<i class="fas fa-align-right"></i>',
            'Căn phải'
          );
          break;
        case 'alignJustify':
          buttonElement = this.createToolbarButton(
            'justifyFull',
            '<i class="fas fa-align-justify"></i>',
            'Căn đều'
          );
          break;
        case 'bulletList':
          buttonElement = this.createToolbarButton(
            'insertUnorderedList',
            '<i class="fas fa-list-ul"></i>',
            'Danh sách không thứ tự'
          );
          break;
        case 'numberedList':
          buttonElement = this.createToolbarButton(
            'insertOrderedList',
            '<i class="fas fa-list-ol"></i>',
            'Danh sách có thứ tự'
          );
          break;
        case 'link':
          buttonElement = this.createToolbarButton(
            'link',
            '<i class="fas fa-link"></i>',
            'Chèn liên kết'
          );
          break;
        case 'image':
          buttonElement = this.createToolbarButton(
            'image',
            '<i class="fas fa-image"></i>',
            'Chèn hình ảnh'
          );
          break;
        case 'table':
          buttonElement = this.createToolbarButton(
            'table',
            '<i class="fas fa-table"></i>',
            'Chèn bảng'
          );
          break;
        case 'undo':
          buttonElement = this.createToolbarButton(
            'undo',
            '<i class="fas fa-undo"></i>',
            'Hoàn tác (Ctrl+Z)'
          );
          break;
        case 'redo':
          buttonElement = this.createToolbarButton(
            'redo',
            '<i class="fas fa-redo"></i>',
            'Làm lại (Ctrl+Y)'
          );
          break;
        case 'clear':
          buttonElement = this.createToolbarButton(
            'clear',
            '<i class="fas fa-trash-alt"></i>',
            'Xóa tất cả'
          );
          break;
        case 'source':
          buttonElement = this.createToolbarButton(
            'source',
            '<i class="fas fa-code"></i>',
            'Xem mã HTML'
          );
          break;
        default:
          buttonElement = null;
      }

      if (buttonElement) {
        this.toolbar.appendChild(buttonElement);
      }
    });

    // Thêm thanh công cụ vào container
    this.container.appendChild(this.toolbar);
  }

  /**
   * Tạo nút trên thanh công cụ
   */
  createToolbarButton(command, icon, title) {
    const button = document.createElement('button');
    button.type = 'button';
    button.className = 'simple-editor-button';
    button.innerHTML = icon;
    button.title = title;
    button.dataset.command = command;

    return button;
  }

  /**
   * Tạo color picker
   */
  createColorPicker(command, icon, title) {
    const wrapper = document.createElement('div');
    wrapper.className = 'simple-editor-color-picker';

    const button = document.createElement('button');
    button.type = 'button';
    button.className = 'simple-editor-button';
    button.innerHTML = icon;
    button.title = title;

    // Tạo dropdown cho color picker
    const dropdown = document.createElement('div');
    dropdown.className = 'simple-editor-color-dropdown';

    // Tạo input color
    const input = document.createElement('input');
    input.type = 'color';
    input.className = 'simple-editor-color-input';
    input.dataset.command = command;

    // Tạo container cho các màu gần đây
    const recentColors = document.createElement('div');
    recentColors.className = 'simple-editor-recent-colors';

    // Lấy các màu gần đây từ localStorage
    const recentColorsList = this.getRecentColors();

    // Thêm các màu gần đây vào dropdown
    recentColorsList.forEach((color) => {
      const colorItem = document.createElement('div');
      colorItem.className = 'simple-editor-color-item';
      colorItem.style.backgroundColor = color;
      colorItem.dataset.color = color;
      colorItem.title = color;
      colorItem.addEventListener('click', () => {
        this.executeCommand(command, color);
        this.addRecentColor(color);
        dropdown.classList.remove('show');
      });
      recentColors.appendChild(colorItem);
    });

    // Thêm label cho input color
    const customColorLabel = document.createElement('div');
    customColorLabel.className = 'simple-editor-color-label';
    customColorLabel.textContent = 'Chọn màu tùy chỉnh:';

    // Thêm các thành phần vào dropdown
    if (recentColorsList.length > 0) {
      const recentColorsLabel = document.createElement('div');
      recentColorsLabel.className = 'simple-editor-color-label';
      recentColorsLabel.textContent = 'Màu gần đây:';
      dropdown.appendChild(recentColorsLabel);
      dropdown.appendChild(recentColors);
      dropdown.appendChild(document.createElement('hr'));
    }

    dropdown.appendChild(customColorLabel);
    dropdown.appendChild(input);

    // Thêm sự kiện click cho button
    button.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      dropdown.classList.toggle('show');
    });

    // Thêm sự kiện click bên ngoài để đóng dropdown
    document.addEventListener('click', (e) => {
      if (!wrapper.contains(e.target)) {
        dropdown.classList.remove('show');
      }
    });

    // Thêm các thành phần vào wrapper
    wrapper.appendChild(button);
    wrapper.appendChild(dropdown);

    return wrapper;
  }

  /**
   * Lấy danh sách màu gần đây
   */
  getRecentColors() {
    const recentColors = localStorage.getItem('simpleEditorRecentColors');
    return recentColors ? JSON.parse(recentColors) : [];
  }

  /**
   * Thêm màu vào danh sách màu gần đây
   */
  addRecentColor(color) {
    let recentColors = this.getRecentColors();

    // Xóa màu này nếu đã tồn tại trong danh sách
    recentColors = recentColors.filter((c) => c !== color);

    // Thêm màu mới vào đầu danh sách
    recentColors.unshift(color);

    // Giới hạn số lượng màu gần đây
    if (recentColors.length > 5) {
      recentColors = recentColors.slice(0, 5);
    }

    // Lưu vào localStorage
    localStorage.setItem(
      'simpleEditorRecentColors',
      JSON.stringify(recentColors)
    );

    return recentColors;
  }

  /**
   * Tạo dropdown
   */
  createDropdown(command, icon, title, options) {
    const wrapper = document.createElement('div');
    wrapper.className = 'simple-editor-dropdown';

    const button = document.createElement('button');
    button.type = 'button';
    button.className = 'simple-editor-button';
    button.innerHTML = icon;
    button.title = title;

    const dropdown = document.createElement('div');
    dropdown.className = 'simple-editor-dropdown-menu';

    options.forEach((option) => {
      const item = document.createElement('a');
      item.href = '#';
      item.className = 'simple-editor-dropdown-item';
      item.textContent = option.text;
      item.dataset.command = command;
      item.dataset.value = option.value;

      dropdown.appendChild(item);
    });

    wrapper.appendChild(button);
    wrapper.appendChild(dropdown);

    return wrapper;
  }

  /**
   * Tạo khu vực soạn thảo
   */
  createEditorArea() {
    // Tạo khu vực soạn thảo
    this.editorArea = document.createElement('div');
    this.editorArea.className = 'simple-editor-content';
    this.editorArea.contentEditable = true;
    this.editorArea.placeholder = this.options.placeholder;

    // Thêm khu vực soạn thảo vào container
    this.container.appendChild(this.editorArea);
  }

  /**
   * Khởi tạo sự kiện
   */
  initEvents() {
    // Sự kiện cho các nút trên thanh công cụ
    this.toolbar.addEventListener('click', (e) => {
      const target = e.target.closest(
        '.simple-editor-button, .simple-editor-dropdown-item'
      );

      if (!target) return;

      e.preventDefault();

      const command = target.dataset.command;
      const value = target.dataset.value || null;

      this.executeCommand(command, value);
    });

    // Sự kiện cho color picker
    const colorInputs = this.toolbar.querySelectorAll(
      '.simple-editor-color-input'
    );
    colorInputs.forEach((input) => {
      input.addEventListener('change', (e) => {
        const command = e.target.dataset.command;
        const value = e.target.value;

        this.executeCommand(command, value);
        this.addRecentColor(value);
      });
    });

    // Sự kiện khi nội dung thay đổi
    this.editorArea.addEventListener('input', () => {
      this.updateValue();
    });

    // Sự kiện khi paste
    this.editorArea.addEventListener('paste', (e) => {
      e.preventDefault();

      // Lấy text từ clipboard
      const text = (e.originalEvent || e).clipboardData.getData('text/plain');

      // Chèn text vào vị trí hiện tại
      document.execCommand('insertText', false, text);
    });

    // Xử lý sự kiện keydown để giữ định dạng chú thích ảnh khi xuống dòng
    this.editorArea.addEventListener('keydown', (e) => {
      // Kiểm tra nếu đang ở trong figcaption và nhấn Enter
      if (e.key === 'Enter') {
        const selection = window.getSelection();
        const range = selection.getRangeAt(0);
        const node = range.startContainer;

        // Kiểm tra xem có đang ở trong figcaption không
        const figcaption = node.closest
          ? node.closest('.simple-editor-caption')
          : node.parentNode
          ? node.parentNode.closest('.simple-editor-caption')
          : null;

        if (figcaption) {
          // Kiểm tra xem con trỏ có đang ở cuối figcaption không
          const isAtEndOfCaption = this.isCaretAtEnd(figcaption);

          if (isAtEndOfCaption) {
            // Nếu ở cuối figcaption, cho phép thoát khỏi khối hình ảnh
            // Tìm container của hình ảnh
            const figure = figcaption.closest('.simple-editor-figure');
            const imageContainer = figure
              ? figure.closest('.simple-editor-image-container')
              : null;

            // Sử dụng container nếu có, nếu không thì sử dụng figure
            const targetElement = imageContainer || figure;

            if (targetElement) {
              e.preventDefault(); // Ngăn chặn hành vi mặc định của Enter

              // Tạo một đoạn văn mới sau khối container/figure
              const newParagraph = document.createElement('p');
              newParagraph.innerHTML = '<br>'; // Thêm <br> để đảm bảo đoạn văn không bị thu gọn

              // Chèn đoạn văn mới sau khối container/figure
              if (targetElement.nextSibling) {
                targetElement.parentNode.insertBefore(
                  newParagraph,
                  targetElement.nextSibling
                );
              } else {
                targetElement.parentNode.appendChild(newParagraph);
              }

              // Di chuyển con trỏ đến đoạn văn mới
              const newRange = document.createRange();
              newRange.setStart(newParagraph, 0);
              newRange.collapse(true);

              selection.removeAllRanges();
              selection.addRange(newRange);

              // Cập nhật giá trị
              this.updateValue();

              return false;
            }
          } else {
            // Nếu không ở cuối figcaption, vẫn giữ hành vi hiện tại
            e.preventDefault(); // Ngăn chặn hành vi mặc định của Enter

            // Chèn thẻ <br> thay vì xuống dòng mới
            document.execCommand('insertHTML', false, '<br>');

            return false;
          }
        }
      }
    });
  }

  /**
   * Thực thi lệnh
   */
  executeCommand(command, value = null) {
    // Focus vào khu vực soạn thảo
    this.editorArea.focus();

    switch (command) {
      case 'clear':
        this.setContent('');
        break;
      case 'link':
        this.insertLink();
        break;
      case 'image':
        this.insertImage();
        break;
      case 'table':
        this.insertTable();
        break;
      case 'source':
        this.toggleSource();
        break;
      case 'heading':
        this.formatBlock(value);
        break;
      case 'paragraph':
        this.formatBlock('p');
        break;
      default:
        document.execCommand(command, false, value);
    }

    // Cập nhật giá trị
    this.updateValue();
  }

  /**
   * Chèn liên kết
   */
  insertLink() {
    const url = prompt('Nhập URL:', 'http://');

    if (url) {
      document.execCommand('createLink', false, url);
    }
  }

  /**
   * Chèn hình ảnh
   */
  insertImage() {
    // Tạo input file ẩn
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.style.display = 'none';

    // Thêm input vào body
    document.body.appendChild(input);

    // Xử lý sự kiện khi chọn file
    input.addEventListener('change', () => {
      if (input.files && input.files[0]) {
        // Tạo FormData
        const formData = new FormData();
        formData.append('image', input.files[0]);

        // Hiển thị thông báo đang tải lên
        const loadingId = this.showNotification('Đang tải lên...', 'info');

        // Gửi request upload
        fetch(this.options.uploadUrl, {
          method: 'POST',
          body: formData,
        })
          .then((response) => response.json())
          .then((data) => {
            // Ẩn thông báo đang tải lên
            this.hideNotification(loadingId);

            if (data.success) {
              // Chèn hình ảnh với caption
              const imageHtml = `
                            <div class="simple-editor-image-container">
                                <figure class="simple-editor-figure">
                                    <img src="${data.url}" alt="${
                data.alt || ''
              }" class="simple-editor-image">
                                    <figcaption class="simple-editor-caption" contenteditable="true">Nhập chú thích ảnh...</figcaption>
                                </figure>
                            </div>
                        `;

              this.insertHTML(imageHtml);

              // Hiển thị thông báo thành công
              this.showNotification('Tải lên thành công!', 'success', 3000);
            } else {
              // Hiển thị thông báo lỗi
              this.showNotification(
                data.message || 'Tải lên thất bại!',
                'error',
                3000
              );
            }
          })
          .catch((error) => {
            // Ẩn thông báo đang tải lên
            this.hideNotification(loadingId);

            // Hiển thị thông báo lỗi
            this.showNotification('Có lỗi xảy ra khi tải lên!', 'error', 3000);
            console.error('Upload error:', error);
          })
          .finally(() => {
            // Xóa input
            document.body.removeChild(input);
          });
      }
    });

    // Kích hoạt click vào input
    input.click();
  }

  /**
   * Chèn bảng
   */
  insertTable() {
    const rows = prompt('Số hàng:', '3');
    const cols = prompt('Số cột:', '3');

    if (rows && cols) {
      let tableHtml = '<table class="simple-editor-table">';

      // Tạo header
      tableHtml += '<thead><tr>';
      for (let i = 0; i < cols; i++) {
        tableHtml += '<th>Tiêu đề ' + (i + 1) + '</th>';
      }
      tableHtml += '</tr></thead>';

      // Tạo body
      tableHtml += '<tbody>';
      for (let i = 0; i < rows - 1; i++) {
        tableHtml += '<tr>';
        for (let j = 0; j < cols; j++) {
          tableHtml += '<td>Nội dung</td>';
        }
        tableHtml += '</tr>';
      }
      tableHtml += '</tbody>';

      tableHtml += '</table>';

      this.insertHTML(tableHtml);
    }
  }

  /**
   * Chèn HTML
   */
  insertHTML(html) {
    document.execCommand('insertHTML', false, html);
  }

  /**
   * Format block
   */
  formatBlock(tag) {
    document.execCommand('formatBlock', false, `<${tag}>`);
  }

  /**
   * Chuyển đổi giữa chế độ soạn thảo và xem mã HTML
   */
  toggleSource() {
    if (this.sourceMode) {
      // Chuyển từ chế độ xem mã HTML sang chế độ soạn thảo
      this.editorArea.innerHTML = this.sourceTextarea.value;
      this.container.removeChild(this.sourceTextarea);
      this.editorArea.style.display = 'block';
      this.sourceMode = false;
    } else {
      // Chuyển từ chế độ soạn thảo sang chế độ xem mã HTML
      this.sourceTextarea = document.createElement('textarea');
      this.sourceTextarea.className = 'simple-editor-source';
      this.sourceTextarea.value = this.editorArea.innerHTML;
      this.container.appendChild(this.sourceTextarea);
      this.editorArea.style.display = 'none';
      this.sourceMode = true;
    }
  }

  /**
   * Hiển thị thông báo
   */
  showNotification(message, type, duration = 0) {
    // Tạo thông báo
    const notification = document.createElement('div');
    notification.className = `simple-editor-notification simple-editor-notification-${type}`;
    notification.textContent = message;

    // Tạo ID ngẫu nhiên
    const id = 'notification-' + Math.random().toString(36).substr(2, 9);
    notification.id = id;

    // Thêm thông báo vào container
    this.container.appendChild(notification);

    // Tự động ẩn sau duration
    if (duration > 0) {
      setTimeout(() => {
        this.hideNotification(id);
      }, duration);
    }

    return id;
  }

  /**
   * Ẩn thông báo
   */
  hideNotification(id) {
    const notification = document.getElementById(id);

    if (notification) {
      notification.remove();
    }
  }

  /**
   * Cập nhật giá trị
   */
  updateValue() {
    // Lấy nội dung từ khu vực soạn thảo
    let content;

    if (this.sourceMode) {
      content = this.sourceTextarea.value;
    } else {
      content = this.editorArea.innerHTML;
    }

    // Cập nhật giá trị cho element gốc
    this.originalElement.value = content;

    // Gọi callback onChange nếu có
    if (typeof this.options.onChange === 'function') {
      this.options.onChange(content);
    }
  }

  /**
   * Thiết lập nội dung
   */
  setContent(content) {
    if (this.sourceMode) {
      this.sourceTextarea.value = content;
    } else {
      this.editorArea.innerHTML = content;
    }

    this.updateValue();
  }

  /**
   * Lấy nội dung
   */
  getContent() {
    return this.originalElement.value;
  }

  /**
   * Kiểm tra xem con trỏ có đang ở cuối phần tử hay không
   * @param {HTMLElement} element - Phần tử cần kiểm tra
   * @returns {boolean} - true nếu con trỏ ở cuối phần tử, false nếu không
   */
  isCaretAtEnd(element) {
    const selection = window.getSelection();
    if (!selection.rangeCount) return false;

    const range = selection.getRangeAt(0);
    const preCaretRange = range.cloneRange();
    preCaretRange.selectNodeContents(element);
    preCaretRange.setEnd(range.endContainer, range.endOffset);

    // Lấy độ dài nội dung từ đầu phần tử đến vị trí con trỏ
    const caretOffset = preCaretRange.toString().length;

    // Lấy độ dài toàn bộ nội dung của phần tử
    const elementText = element.textContent || '';
    const elementLength = elementText.length;

    // Nếu độ dài từ đầu phần tử đến vị trí con trỏ bằng độ dài toàn bộ nội dung
    // thì con trỏ đang ở cuối phần tử
    return caretOffset === elementLength;
  }
}
