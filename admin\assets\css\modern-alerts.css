/**
 * Modern Alerts CSS - N<PERSON>i Thất Bàng <PERSON>
 * Thiết kế hiện đại cho các thông báo trong trang admin
 */

:root {
    /* <PERSON><PERSON><PERSON> sắc cho các loại thông báo */
    --alert-success-bg: #d1e7dd;
    --alert-success-border: #badbcc;
    --alert-success-text: #0f5132;
    --alert-success-icon: #198754;

    --alert-info-bg: #cff4fc;
    --alert-info-border: #b6effb;
    --alert-info-text: #055160;
    --alert-info-icon: #0dcaf0;

    --alert-warning-bg: #fff3cd;
    --alert-warning-border: #ffecb5;
    --alert-warning-text: #664d03;
    --alert-warning-icon: #ffc107;

    --alert-danger-bg: #f8d7da;
    --alert-danger-border: #f5c2c7;
    --alert-danger-text: #842029;
    --alert-danger-icon: #dc3545;

    /* <PERSON><PERSON><PERSON> */
    --alert-transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    --alert-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
    --alert-border-radius: 0.75rem;
}

/* Container cho thông báo */
.alert-container {
    margin-bottom: 1.5rem;
    position: relative;
}

/* Thiết kế chung cho tất cả các thông báo */
.alert {
    display: flex;
    align-items: flex-start;
    padding: 1rem 1.25rem;
    border-radius: var(--alert-border-radius);
    box-shadow: var(--alert-shadow);
    position: relative;
    border: none;
    margin-bottom: 1rem;
    transition: var(--alert-transition);
}

.alert:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Icon cho thông báo */
.alert::before {
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 1.1rem;
    margin-right: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
    line-height: 1;
}

/* Nội dung thông báo */
.alert-content {
    flex: 1;
}

/* Tiêu đề thông báo */
.alert-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

/* Nút đóng thông báo */
.alert .close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    opacity: 0.5;
    transition: var(--alert-transition);
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1;
    color: inherit;
    background: transparent;
    border: 0;
    padding: 0;
    margin: 0;
}

.alert .close:hover {
    opacity: 1;
}

/* Thông báo thành công */
.alert-success {
    background-color: var(--alert-success-bg);
    border-left: 4px solid var(--alert-success-border);
    color: var(--alert-success-text);
}

.alert-success::before {
    content: '\f058'; /* check-circle */
    color: var(--alert-success-icon);
}

/* Thông báo thông tin */
.alert-info {
    background-color: var(--alert-info-bg);
    border-left: 4px solid var(--alert-info-border);
    color: var(--alert-info-text);
}

.alert-info::before {
    content: '\f05a'; /* info-circle */
    color: var(--alert-info-icon);
}

/* Thông báo cảnh báo */
.alert-warning {
    background-color: var(--alert-warning-bg);
    border-left: 4px solid var(--alert-warning-border);
    color: var(--alert-warning-text);
}

.alert-warning::before {
    content: '\f071'; /* exclamation-triangle */
    color: var(--alert-warning-icon);
}

/* Thông báo lỗi */
.alert-danger {
    background-color: var(--alert-danger-bg);
    border-left: 4px solid var(--alert-danger-border);
    color: var(--alert-danger-text);
}

.alert-danger::before {
    content: '\f06a'; /* exclamation-circle */
    color: var(--alert-danger-icon);
}

/* Hiệu ứng khi thông báo xuất hiện */
@keyframes alertFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.alert {
    animation: alertFadeIn 0.3s ease-out forwards;
}

/* Hiệu ứng khi thông báo biến mất */
@keyframes alertFadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

.alert.fade-out,
.custom-alert.fade-out {
    animation: alertFadeOut 0.3s ease-in forwards;
}

/* Hiệu ứng fade-in cho custom-alert */
@keyframes customAlertFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.custom-alert.fade-in {
    animation: customAlertFadeIn 0.3s ease-out forwards;
}

/* Responsive */
@media (max-width: 768px) {
    .alert {
        padding: 0.75rem 1rem;
    }

    .alert::before {
        font-size: 1rem;
        margin-right: 0.75rem;
    }

    .alert-title {
        font-size: 0.9rem;
    }

    .alert .close {
        top: 0.75rem;
        right: 0.75rem;
        font-size: 1rem;
    }
}
