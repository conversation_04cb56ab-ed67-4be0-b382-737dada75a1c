<?php
// Include init
require_once '../includes/init.php';

// Ki<PERSON>m tra quyền admin
require_once 'partials/check_admin.php';

// Include footer helper để sử dụng hàm get_setting()
if (!function_exists('get_setting')) {
    require_once '../includes/footer-helper.php';
}

// Thiết lập tiêu đề trang
$page_title = 'Quản lý Footer';

// Xử lý lưu cài đặt
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Bắt đầu transaction
        $conn->beginTransaction();

        // Cập nhật thông tin cột 1 (Giới thiệu)
        $footer_col1_content = isset($_POST['footer_col1_content']) ? $_POST['footer_col1_content'] : '';
        update_setting('footer_col1_content', $footer_col1_content);

        // Cập nhật thông tin công ty
        $company_name = isset($_POST['company_name']) ? $_POST['company_name'] : '';
        $company_address = isset($_POST['company_address']) ? $_POST['company_address'] : '';
        $company_phone = isset($_POST['company_phone']) ? $_POST['company_phone'] : '';
        $company_email = isset($_POST['company_email']) ? $_POST['company_email'] : '';
        $business_hours = isset($_POST['business_hours']) ? $_POST['business_hours'] : '';

        update_setting('company_name', $company_name);
        update_setting('company_address', $company_address);
        update_setting('company_phone', $company_phone);
        update_setting('company_email', $company_email);
        update_setting('business_hours', $business_hours);

        // Cập nhật tiêu đề các cột
        $footer_col2_title = isset($_POST['footer_col2_title']) ? $_POST['footer_col2_title'] : '';
        $footer_col3_title = isset($_POST['footer_col3_title']) ? $_POST['footer_col3_title'] : '';
        $footer_col4_title = isset($_POST['footer_col4_title']) ? $_POST['footer_col4_title'] : '';

        update_setting('footer_col2_title', $footer_col2_title);
        update_setting('footer_col3_title', $footer_col3_title);
        update_setting('footer_col4_title', $footer_col4_title);

        // Cập nhật liên kết nhanh (cột 2)
        $quick_links = [];
        if (isset($_POST['quick_link_text']) && isset($_POST['quick_link_url'])) {
            $link_texts = $_POST['quick_link_text'];
            $link_urls = $_POST['quick_link_url'];

            for ($i = 0; $i < count($link_texts); $i++) {
                if (!empty($link_texts[$i]) && !empty($link_urls[$i])) {
                    $quick_links[] = [
                        'text' => $link_texts[$i],
                        'url' => $link_urls[$i]
                    ];
                }
            }
        }
        update_setting('footer_col2_links', json_encode($quick_links, JSON_UNESCAPED_UNICODE));

        // Cập nhật liên kết hỗ trợ khách hàng (cột 3)
        $support_links = [];
        if (isset($_POST['support_link_text']) && isset($_POST['support_link_url'])) {
            $link_texts = $_POST['support_link_text'];
            $link_urls = $_POST['support_link_url'];

            for ($i = 0; $i < count($link_texts); $i++) {
                if (!empty($link_texts[$i]) && !empty($link_urls[$i])) {
                    $support_links[] = [
                        'text' => $link_texts[$i],
                        'url' => $link_urls[$i]
                    ];
                }
            }
        }
        update_setting('footer_col3_links', json_encode($support_links, JSON_UNESCAPED_UNICODE));

        // Cập nhật mạng xã hội
        $facebook_url = isset($_POST['facebook_url']) ? $_POST['facebook_url'] : '';
        $instagram_url = isset($_POST['instagram_url']) ? $_POST['instagram_url'] : '';
        $youtube_url = isset($_POST['youtube_url']) ? $_POST['youtube_url'] : '';
        $tiktok_url = isset($_POST['tiktok_url']) ? $_POST['tiktok_url'] : '';
        $zalo_url = isset($_POST['zalo_url']) ? $_POST['zalo_url'] : '';

        update_setting('facebook_url', $facebook_url);
        update_setting('instagram_url', $instagram_url);
        update_setting('youtube_url', $youtube_url);
        update_setting('tiktok_url', $tiktok_url);
        update_setting('zalo_url', $zalo_url);

        // Cập nhật danh sách chứng nhận
        $certifications = [];
        if (isset($_POST['cert_name']) && isset($_POST['cert_url']) && isset($_POST['cert_image'])) {
            $cert_names = $_POST['cert_name'];
            $cert_urls = $_POST['cert_url'];
            $cert_images = $_POST['cert_image'];

            for ($i = 0; $i < count($cert_names); $i++) {
                if (!empty($cert_urls[$i]) && !empty($cert_images[$i])) {
                    $certifications[] = [
                        'name' => $cert_names[$i],
                        'url' => $cert_urls[$i],
                        'image' => $cert_images[$i]
                    ];
                }
            }
        }

        // Nếu không có chứng nhận nào, thêm chứng nhận Bộ Công Thương mặc định
        if (empty($certifications)) {
            $certifications[] = [
                'name' => 'Bộ Công Thương',
                'url' => 'http://online.gov.vn/',
                'image' => '/uploads/footer/bocongthong.png'
            ];
        }

        update_setting('footer_certifications', json_encode($certifications, JSON_UNESCAPED_UNICODE));

        // Cập nhật thông tin bản quyền
        $footer_copyright_text = isset($_POST['footer_copyright_text']) ? $_POST['footer_copyright_text'] : '';
        update_setting('footer_copyright_text', $footer_copyright_text);

        // Commit transaction
        $conn->commit();

        $success_message = 'Cập nhật thông tin footer thành công!';
    } catch (Exception $e) {
        // Rollback transaction nếu có lỗi và nếu có giao dịch đang hoạt động
        try {
            if ($conn && $conn->inTransaction()) {
                $conn->rollBack();
            }
        } catch (PDOException $rollbackException) {
            // Ghi log lỗi nếu cần
            error_log("Lỗi khi rollback: " . $rollbackException->getMessage());
        }

        $error_message = 'Đã xảy ra lỗi: ' . $e->getMessage();
    }
}

// Lấy dữ liệu hiện tại
$footer_col1_content = get_setting('footer_col1_content', SITE_NAME . ' cung cấp các sản phẩm nội thất cao cấp, chất lượng với giá thành hợp lý. Chúng tôi tự hào mang đến những sản phẩm đẹp, bền và phù hợp với mọi không gian sống.');
$company_name = get_setting('company_name', SITE_NAME);
$company_address = get_setting('company_address', '');
$company_phone = get_setting('company_phone', '');
$company_email = get_setting('company_email', '');
$business_hours = get_setting('business_hours', '');

$footer_col2_title = get_setting('footer_col2_title', 'Liên kết nhanh');
$footer_col3_title = get_setting('footer_col3_title', 'Hỗ trợ khách hàng');
$footer_col4_title = get_setting('footer_col4_title', 'Kết nối với chúng tôi');

$quick_links_json = get_setting('footer_col2_links', '[]');
$quick_links = json_decode($quick_links_json, true);
if (!is_array($quick_links)) {
    $quick_links = [];
}

$support_links_json = get_setting('footer_col3_links', '[]');
$support_links = json_decode($support_links_json, true);
if (!is_array($support_links)) {
    $support_links = [];
}

$facebook_url = get_setting('facebook_url', '');
$instagram_url = get_setting('instagram_url', '');
$youtube_url = get_setting('youtube_url', '');
$tiktok_url = get_setting('tiktok_url', '');
$zalo_url = get_setting('zalo_url', '');

$footer_copyright_text = get_setting('footer_copyright_text', 'Tất cả quyền được bảo lưu.');

// Include header
include_once 'partials/header.php';
?>

<!-- Content -->
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Quản lý Footer</h1>

    <?php if (!empty($success_message)): ?>
    <div class="alert alert-success">
        <?php echo $success_message; ?>
    </div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
    <div class="alert alert-danger">
        <?php echo $error_message; ?>
    </div>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Cài đặt Footer</h6>
        </div>
        <div class="card-body">
            <form method="post" action="">
                <ul class="nav nav-tabs" id="footerTabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="general-tab" data-toggle="tab" href="#general" role="tab" aria-controls="general" aria-selected="true">Thông tin chung</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="quick-links-tab" data-toggle="tab" href="#quick-links" role="tab" aria-controls="quick-links" aria-selected="false">Liên kết nhanh</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="support-links-tab" data-toggle="tab" href="#support-links" role="tab" aria-controls="support-links" aria-selected="false">Hỗ trợ khách hàng</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="social-tab" data-toggle="tab" href="#social" role="tab" aria-controls="social" aria-selected="false">Mạng xã hội</a>
                    </li>
                </ul>

                <div class="tab-content mt-4" id="footerTabsContent">
                    <!-- Tab Thông tin chung -->
                    <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                        <div class="form-group">
                            <label for="footer_col1_content">Giới thiệu công ty</label>
                            <textarea class="form-control" id="footer_col1_content" name="footer_col1_content" rows="4"><?php echo htmlspecialchars($footer_col1_content); ?></textarea>
                        </div>

                        <div class="form-group">
                            <label for="company_name">Tên công ty</label>
                            <input type="text" class="form-control" id="company_name" name="company_name" value="<?php echo htmlspecialchars($company_name); ?>">
                        </div>

                        <div class="form-group">
                            <label for="company_address">Địa chỉ</label>
                            <input type="text" class="form-control" id="company_address" name="company_address" value="<?php echo htmlspecialchars($company_address); ?>">
                        </div>

                        <div class="form-group">
                            <label for="company_phone">Số điện thoại</label>
                            <input type="text" class="form-control" id="company_phone" name="company_phone" value="<?php echo htmlspecialchars($company_phone); ?>">
                        </div>

                        <div class="form-group">
                            <label for="company_email">Email</label>
                            <input type="email" class="form-control" id="company_email" name="company_email" value="<?php echo htmlspecialchars($company_email); ?>">
                        </div>

                        <div class="form-group">
                            <label for="business_hours">Giờ làm việc</label>
                            <input type="text" class="form-control" id="business_hours" name="business_hours" value="<?php echo htmlspecialchars($business_hours); ?>">
                        </div>

                        <div class="form-group">
                            <label for="footer_copyright_text">Nội dung bản quyền (hiển thị đầy đủ)</label>
                            <input type="text" class="form-control" id="footer_copyright_text" name="footer_copyright_text" value="<?php echo htmlspecialchars($footer_copyright_text); ?>" placeholder="© <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. Tất cả quyền được bảo lưu.">
                            <small class="form-text text-muted">Nhập đầy đủ nội dung bản quyền bạn muốn hiển thị ở chân trang, bao gồm cả ký hiệu © và năm nếu cần.</small>
                        </div>

                        <h5 class="mt-4">Tiêu đề các cột</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="footer_col2_title">Tiêu đề cột 2</label>
                                    <input type="text" class="form-control" id="footer_col2_title" name="footer_col2_title" value="<?php echo htmlspecialchars($footer_col2_title); ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="footer_col3_title">Tiêu đề cột 3</label>
                                    <input type="text" class="form-control" id="footer_col3_title" name="footer_col3_title" value="<?php echo htmlspecialchars($footer_col3_title); ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="footer_col4_title">Tiêu đề cột 4</label>
                                    <input type="text" class="form-control" id="footer_col4_title" name="footer_col4_title" value="<?php echo htmlspecialchars($footer_col4_title); ?>">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tab Liên kết nhanh -->
                    <div class="tab-pane fade" id="quick-links" role="tabpanel" aria-labelledby="quick-links-tab">
                        <div class="quick-links-container">
                            <?php if (empty($quick_links)): ?>
                                <div class="quick-link-item mb-3">
                                    <div class="row">
                                        <div class="col-md-5">
                                            <div class="form-group">
                                                <label>Tên liên kết</label>
                                                <input type="text" class="form-control" name="quick_link_text[]" placeholder="Tên liên kết">
                                            </div>
                                        </div>
                                        <div class="col-md-5">
                                            <div class="form-group">
                                                <label>URL</label>
                                                <input type="text" class="form-control" name="quick_link_url[]" placeholder="/duong-dan">
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label>&nbsp;</label>
                                            <button type="button" class="btn btn-danger btn-block remove-link">Xóa</button>
                                        </div>
                                    </div>
                                </div>
                            <?php else: ?>
                                <?php foreach ($quick_links as $link): ?>
                                <div class="quick-link-item mb-3">
                                    <div class="row">
                                        <div class="col-md-5">
                                            <div class="form-group">
                                                <label>Tên liên kết</label>
                                                <input type="text" class="form-control" name="quick_link_text[]" value="<?php echo htmlspecialchars($link['text']); ?>" placeholder="Tên liên kết">
                                            </div>
                                        </div>
                                        <div class="col-md-5">
                                            <div class="form-group">
                                                <label>URL</label>
                                                <input type="text" class="form-control" name="quick_link_url[]" value="<?php echo htmlspecialchars($link['url']); ?>" placeholder="/duong-dan">
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label>&nbsp;</label>
                                            <button type="button" class="btn btn-danger btn-block remove-link">Xóa</button>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>

                        <button type="button" class="btn btn-success add-quick-link">
                            <i class="fas fa-plus"></i> Thêm liên kết
                        </button>
                    </div>

                    <!-- Tab Hỗ trợ khách hàng -->
                    <div class="tab-pane fade" id="support-links" role="tabpanel" aria-labelledby="support-links-tab">
                        <div class="support-links-container">
                            <?php if (empty($support_links)): ?>
                                <div class="support-link-item mb-3">
                                    <div class="row">
                                        <div class="col-md-5">
                                            <div class="form-group">
                                                <label>Tên liên kết</label>
                                                <input type="text" class="form-control" name="support_link_text[]" placeholder="Tên liên kết">
                                            </div>
                                        </div>
                                        <div class="col-md-5">
                                            <div class="form-group">
                                                <label>URL</label>
                                                <input type="text" class="form-control" name="support_link_url[]" placeholder="/duong-dan">
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label>&nbsp;</label>
                                            <button type="button" class="btn btn-danger btn-block remove-link">Xóa</button>
                                        </div>
                                    </div>
                                </div>
                            <?php else: ?>
                                <?php foreach ($support_links as $link): ?>
                                <div class="support-link-item mb-3">
                                    <div class="row">
                                        <div class="col-md-5">
                                            <div class="form-group">
                                                <label>Tên liên kết</label>
                                                <input type="text" class="form-control" name="support_link_text[]" value="<?php echo htmlspecialchars($link['text']); ?>" placeholder="Tên liên kết">
                                            </div>
                                        </div>
                                        <div class="col-md-5">
                                            <div class="form-group">
                                                <label>URL</label>
                                                <input type="text" class="form-control" name="support_link_url[]" value="<?php echo htmlspecialchars($link['url']); ?>" placeholder="/duong-dan">
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label>&nbsp;</label>
                                            <button type="button" class="btn btn-danger btn-block remove-link">Xóa</button>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>

                        <button type="button" class="btn btn-success add-support-link">
                            <i class="fas fa-plus"></i> Thêm liên kết
                        </button>
                    </div>

                    <!-- Tab Mạng xã hội -->
                    <div class="tab-pane fade" id="social" role="tabpanel" aria-labelledby="social-tab">
                        <h5 class="mb-3">Mạng xã hội</h5>
                        <div class="form-group">
                            <label for="facebook_url">Facebook URL</label>
                            <input type="url" class="form-control" id="facebook_url" name="facebook_url" value="<?php echo htmlspecialchars($facebook_url); ?>" placeholder="https://facebook.com/your-page">
                        </div>

                        <div class="form-group">
                            <label for="instagram_url">Instagram URL</label>
                            <input type="url" class="form-control" id="instagram_url" name="instagram_url" value="<?php echo htmlspecialchars($instagram_url); ?>" placeholder="https://instagram.com/your-account">
                        </div>

                        <div class="form-group">
                            <label for="youtube_url">YouTube URL</label>
                            <input type="url" class="form-control" id="youtube_url" name="youtube_url" value="<?php echo htmlspecialchars($youtube_url); ?>" placeholder="https://youtube.com/your-channel">
                        </div>

                        <div class="form-group">
                            <label for="tiktok_url">TikTok URL</label>
                            <input type="url" class="form-control" id="tiktok_url" name="tiktok_url" value="<?php echo htmlspecialchars($tiktok_url); ?>" placeholder="https://tiktok.com/@your-account">
                        </div>

                        <div class="form-group">
                            <label for="zalo_url">Zalo URL</label>
                            <input type="url" class="form-control" id="zalo_url" name="zalo_url" value="<?php echo htmlspecialchars($zalo_url); ?>" placeholder="https://zalo.me/your-account">
                        </div>

                        <hr class="my-4">

                        <h5 class="mb-3">Chứng nhận và Chứng chỉ</h5>

                        <div class="certifications-container">
                            <?php
                            // Lấy danh sách chứng nhận từ cài đặt
                            $certifications_json = get_setting('footer_certifications', '[]');
                            $certifications = json_decode($certifications_json, true);

                            // Nếu không có chứng nhận nào hoặc dữ liệu không hợp lệ, sử dụng chứng nhận mặc định (Bộ Công Thương)
                            if (!is_array($certifications) || empty($certifications)) {
                                // Lấy URL và đường dẫn hình ảnh từ cài đặt cũ (để tương thích ngược)
                                $bct_url = get_setting('bct_url', 'http://online.gov.vn/');
                                $bct_image = get_setting('bct_image', '/uploads/footer/bocongthong.png');

                                $certifications = [
                                    [
                                        'name' => 'Bộ Công Thương',
                                        'url' => $bct_url,
                                        'image' => $bct_image
                                    ]
                                ];
                            }

                            // Hiển thị tất cả các chứng nhận
                            foreach ($certifications as $index => $cert):
                                $cert_name = isset($cert['name']) ? $cert['name'] : '';
                                $cert_url = isset($cert['url']) ? $cert['url'] : '';
                                $cert_image = isset($cert['image']) ? $cert['image'] : '';
                            ?>
                            <div class="certification-item mb-4 p-3 border rounded">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>Tên chứng nhận</label>
                                            <input type="text" class="form-control" name="cert_name[]" value="<?php echo htmlspecialchars($cert_name); ?>" placeholder="Bộ Công Thương">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>URL</label>
                                            <input type="url" class="form-control" name="cert_url[]" value="<?php echo htmlspecialchars($cert_url); ?>" placeholder="http://online.gov.vn/">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>Đường dẫn hình ảnh</label>
                                            <input type="text" class="form-control" name="cert_image[]" value="<?php echo htmlspecialchars($cert_image); ?>" placeholder="/uploads/footer/bocongthong.png">
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-8">
                                        <small class="form-text text-muted">Đường dẫn hình ảnh tương đối từ thư mục gốc của website hoặc URL đầy đủ</small>
                                    </div>
                                    <div class="col-md-4 text-right">
                                        <button type="button" class="btn btn-danger remove-certification">
                                            <i class="fas fa-trash"></i> Xóa chứng nhận
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>

                        <button type="button" class="btn btn-success add-certification mb-4">
                            <i class="fas fa-plus"></i> Thêm chứng nhận mới
                        </button>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Lưu thay đổi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- JavaScript cho trang quản lý footer -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Thêm liên kết nhanh
    document.querySelector('.add-quick-link').addEventListener('click', function() {
        const container = document.querySelector('.quick-links-container');
        const template = `
            <div class="quick-link-item mb-3">
                <div class="row">
                    <div class="col-md-5">
                        <div class="form-group">
                            <label>Tên liên kết</label>
                            <input type="text" class="form-control" name="quick_link_text[]" placeholder="Tên liên kết">
                        </div>
                    </div>
                    <div class="col-md-5">
                        <div class="form-group">
                            <label>URL</label>
                            <input type="text" class="form-control" name="quick_link_url[]" placeholder="/duong-dan">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label>&nbsp;</label>
                        <button type="button" class="btn btn-danger btn-block remove-link">Xóa</button>
                    </div>
                </div>
            </div>
        `;
        container.insertAdjacentHTML('beforeend', template);

        // Thêm sự kiện cho nút xóa mới
        const newRemoveButton = container.lastElementChild.querySelector('.remove-link');
        newRemoveButton.addEventListener('click', function() {
            this.closest('.quick-link-item').remove();
        });
    });

    // Thêm liên kết hỗ trợ
    document.querySelector('.add-support-link').addEventListener('click', function() {
        const container = document.querySelector('.support-links-container');
        const template = `
            <div class="support-link-item mb-3">
                <div class="row">
                    <div class="col-md-5">
                        <div class="form-group">
                            <label>Tên liên kết</label>
                            <input type="text" class="form-control" name="support_link_text[]" placeholder="Tên liên kết">
                        </div>
                    </div>
                    <div class="col-md-5">
                        <div class="form-group">
                            <label>URL</label>
                            <input type="text" class="form-control" name="support_link_url[]" placeholder="/duong-dan">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label>&nbsp;</label>
                        <button type="button" class="btn btn-danger btn-block remove-link">Xóa</button>
                    </div>
                </div>
            </div>
        `;
        container.insertAdjacentHTML('beforeend', template);

        // Thêm sự kiện cho nút xóa mới
        const newRemoveButton = container.lastElementChild.querySelector('.remove-link');
        newRemoveButton.addEventListener('click', function() {
            this.closest('.support-link-item').remove();
        });
    });

    // Xóa liên kết
    document.querySelectorAll('.remove-link').forEach(function(button) {
        button.addEventListener('click', function() {
            this.closest('.quick-link-item, .support-link-item').remove();
        });
    });

    // Thêm chứng nhận mới
    document.querySelector('.add-certification').addEventListener('click', function() {
        const container = document.querySelector('.certifications-container');
        const template = `
            <div class="certification-item mb-4 p-3 border rounded">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Tên chứng nhận</label>
                            <input type="text" class="form-control" name="cert_name[]" placeholder="Tên chứng nhận">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>URL</label>
                            <input type="url" class="form-control" name="cert_url[]" placeholder="http://example.com/">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Đường dẫn hình ảnh</label>
                            <input type="text" class="form-control" name="cert_image[]" placeholder="/uploads/footer/certificate.png">
                        </div>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-8">
                        <small class="form-text text-muted">Đường dẫn hình ảnh tương đối từ thư mục gốc của website hoặc URL đầy đủ</small>
                    </div>
                    <div class="col-md-4 text-right">
                        <button type="button" class="btn btn-danger remove-certification">
                            <i class="fas fa-trash"></i> Xóa chứng nhận
                        </button>
                    </div>
                </div>
            </div>
        `;
        container.insertAdjacentHTML('beforeend', template);

        // Thêm sự kiện cho nút xóa mới
        const newRemoveButton = container.lastElementChild.querySelector('.remove-certification');
        newRemoveButton.addEventListener('click', function() {
            this.closest('.certification-item').remove();
        });
    });

    // Xóa chứng nhận
    document.querySelectorAll('.remove-certification').forEach(function(button) {
        button.addEventListener('click', function() {
            this.closest('.certification-item').remove();
        });
    });
});
</script>

<?php
// Include footer
include_once 'partials/footer.php';
?>
