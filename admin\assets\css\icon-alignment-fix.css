/**
 * Icon Alignment Fix - Nội Thất Bàng Vũ Admin
 * CSS chuyên dụng để khắc phục vấn đề căn chỉnh icon trong thông báo
 */

/* Reset và căn chỉnh cơ bản cho tất cả icon */
.alert-icon,
.notification-icon,
.alert::before {
    /* Đảm bảo flexbox hoạt động đúng */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    
    /* Reset line-height và vertical-align */
    line-height: 1 !important;
    vertical-align: middle !important;
    
    /* Đảm bảo text-align center */
    text-align: center !important;
    
    /* Loại bỏ margin/padding không mong muốn */
    box-sizing: border-box;
}

/* Khắc phục cụ thể cho .alert-icon */
.alert-icon {
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    flex-shrink: 0 !important;
    position: relative;
}

.alert-icon i {
    /* Đảm bảo icon nằm chính giữa */
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    
    /* Reset các thuộc tính có thể gây lệch */
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1 !important;
    
    /* Đảm bảo kích thước phù hợp */
    font-size: 18px !important;
    width: auto !important;
    height: auto !important;
}

/* Khắc phục cho pseudo-elements (::before) */
.alert::before {
    /* Đảm bảo pseudo-element nằm chính giữa */
    position: relative;
    top: 0;
    left: 0;
    transform: none;
    
    /* Căn chỉnh text */
    text-align: center !important;
    line-height: 28px !important; /* Bằng với height */
    
    /* Đảm bảo kích thước */
    width: 28px !important;
    height: 28px !important;
    border-radius: 50% !important;
    
    /* Background để dễ nhìn */
    background-color: rgba(255, 255, 255, 0.9) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Khắc phục cho FontAwesome icons */
.fas,
.far,
.fab,
.fa {
    /* Reset các thuộc tính có thể gây lệch */
    vertical-align: middle !important;
    line-height: 1 !important;
    
    /* Đảm bảo không có transform không mong muốn */
    transform: none;
    
    /* Đảm bảo display inline-block */
    display: inline-block;
}

/* Khắc phục cho các icon cụ thể */
.fa-check-circle,
.fa-exclamation-circle,
.fa-exclamation-triangle,
.fa-info-circle {
    /* Đảm bảo các icon này được căn chỉnh hoàn hảo */
    text-align: center !important;
    vertical-align: middle !important;
    line-height: 1 !important;
}

/* Khắc phục cho container thông báo */
.custom-alert,
.alert {
    /* Đảm bảo flexbox alignment */
    display: flex !important;
    align-items: center !important;
    
    /* Đảm bảo không có overflow ẩn icon */
    overflow: visible !important;
}

/* Debug mode - uncomment để debug */
/*
.alert-icon {
    border: 2px solid red !important;
    background-color: yellow !important;
}

.alert-icon i {
    border: 1px solid blue !important;
    background-color: lightblue !important;
}

.alert::before {
    border: 2px solid green !important;
    background-color: lightgreen !important;
}
*/

/* Responsive adjustments */
@media (max-width: 768px) {
    .alert-icon {
        width: 36px !important;
        height: 36px !important;
    }
    
    .alert-icon i {
        font-size: 16px !important;
    }
    
    .alert::before {
        width: 24px !important;
        height: 24px !important;
        line-height: 24px !important;
        font-size: 1rem !important;
    }
}

/* Đảm bảo không có xung đột với CSS khác */
.alert-icon,
.notification-icon {
    /* Override mọi CSS có thể gây xung đột */
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

/* Khắc phục cho các trường hợp đặc biệt */
.custom-alert-success .alert-icon,
.custom-alert-danger .alert-icon,
.custom-alert-warning .alert-icon,
.custom-alert-info .alert-icon {
    /* Đảm bảo border không ảnh hưởng đến căn chỉnh */
    box-sizing: border-box !important;
}

/* Final override để đảm bảo */
.alert-icon i,
.notification-icon i {
    /* Cuối cùng, đảm bảo icon luôn ở giữa */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}
