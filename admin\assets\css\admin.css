/* Admin CSS */

:root {
    --primary: #4e73df;
    --secondary: #858796;
    --success: #1cc88a;
    --info: #36b9cc;
    --warning: #f6c23e;
    --danger: #e74a3b;
    --light: #f8f9fc;
    --dark: #5a5c69;
}

/* Sidebar */
.sidebar {
    width: 14rem;
    min-height: 100vh;
    position: fixed;
    z-index: 1;
    top: 0;
    left: 0;
    background-color: #202834;
    overflow-x: hidden;
    transition: 0.5s;
}

.sidebar .sidebar-brand {
    height: 4.375rem;
    text-decoration: none;
    font-size: 1rem;
    font-weight: 800;
    padding: 1rem 1rem;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
    z-index: 1;
}

.sidebar .sidebar-brand .sidebar-brand-icon i {
    font-size: 2rem;
}

.sidebar .sidebar-brand .sidebar-brand-text {
    display: inline;
}

.sidebar hr.sidebar-divider {
    margin: 0 1rem 1rem;
}

.sidebar .sidebar-heading {
    text-align: left;
    padding: 0 1rem;
    font-weight: 800;
    font-size: 0.65rem;
    text-transform: uppercase;
    color: rgba(255, 255, 255, 0.6);
}

.sidebar .nav-item {
    position: relative;
}

.sidebar .nav-item .nav-link {
    display: block;
    width: 100%;
    text-align: left;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.8);
}

.sidebar .nav-item .nav-link i {
    margin-right: 0.25rem;
}

.sidebar .nav-item .nav-link:hover {
    color: #fff;
}

.sidebar .nav-item .nav-link:active, .sidebar .nav-item .nav-link:focus {
    color: #fff;
}

.sidebar .nav-item.active .nav-link {
    font-weight: 700;
    color: #fff;
}

.sidebar #sidebarToggle {
    width: 2.5rem;
    height: 2.5rem;
    text-align: center;
    margin-bottom: 1rem;
    cursor: pointer;
}

.sidebar.toggled {
    width: 0;
    overflow: hidden;
}

/* Content Wrapper */
#content-wrapper {
    background-color: #f8f9fc;
    width: 100%;
    overflow-x: hidden;
    margin-left: 14rem;
    transition: margin-left 0.5s;
}

#content-wrapper.toggled {
    margin-left: 0;
}

/* Topbar */
.topbar {
    height: 4.375rem;
}

.topbar .navbar-search {
    width: 25rem;
}

.topbar .navbar-search input {
    font-size: 0.85rem;
    height: auto;
}

.topbar .topbar-divider {
    width: 0;
    border-right: 1px solid #e3e6f0;
    height: calc(4.375rem - 2rem);
    margin: auto 1rem;
}

.topbar .nav-item .nav-link {
    height: 4.375rem;
    display: flex;
    align-items: center;
    padding: 0 0.75rem;
}

.topbar .nav-item .nav-link:focus {
    outline: none;
}

.topbar .nav-item:focus {
    outline: none;
}

.topbar .dropdown {
    position: static;
}

.topbar .dropdown .dropdown-menu {
    width: calc(100% - 1.5rem);
    right: 0.75rem;
}

.topbar .dropdown-list {
    padding: 0;
    border: none;
    overflow: hidden;
}

.topbar .dropdown-list .dropdown-header {
    background-color: var(--primary);
    border: 1px solid var(--primary);
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    color: #fff;
}

.topbar .dropdown-list .dropdown-item {
    white-space: normal;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    border-left: 1px solid #e3e6f0;
    border-right: 1px solid #e3e6f0;
    border-bottom: 1px solid #e3e6f0;
    line-height: 1.3rem;
}

.topbar .dropdown-list .dropdown-item .dropdown-list-image {
    position: relative;
    height: 2.5rem;
    width: 2.5rem;
}

.topbar .dropdown-list .dropdown-item .dropdown-list-image img {
    height: 2.5rem;
    width: 2.5rem;
}

.topbar .dropdown-list .dropdown-item .dropdown-list-image .status-indicator {
    background-color: #eaecf4;
    height: 0.75rem;
    width: 0.75rem;
    border-radius: 100%;
    position: absolute;
    bottom: 0;
    right: 0;
    border: 0.125rem solid #fff;
}

.topbar .dropdown-list .dropdown-item .text-truncate {
    max-width: 10rem;
}

.topbar .dropdown-list .dropdown-item:active {
    background-color: #eaecf4;
    color: #3a3b45;
}

@media (min-width: 576px) {
    .topbar .dropdown {
        position: relative;
    }
    .topbar .dropdown .dropdown-menu {
        width: auto;
        right: 0;
    }
    .topbar .dropdown-list {
        width: 20rem !important;
    }
    .topbar .dropdown-list .dropdown-item .text-truncate {
        max-width: 13.375rem;
    }
}

.topbar.navbar-light .navbar-nav .nav-item .nav-link {
    color: #d1d3e2;
}

.topbar.navbar-light .navbar-nav .nav-item .nav-link:hover {
    color: #b7b9cc;
}

.topbar.navbar-light .navbar-nav .nav-item .nav-link:active {
    color: #858796;
}

/* Cards */
.card .card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

.card-header:first-child {
    border-radius: calc(0.35rem - 1px) calc(0.35rem - 1px) 0 0;
}

.card-header {
    padding: 0.75rem 1.25rem;
    margin-bottom: 0;
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

/* Buttons */
.btn-circle {
    border-radius: 100%;
    height: 2.5rem;
    width: 2.5rem;
    font-size: 1rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-circle.btn-sm {
    height: 1.8rem;
    width: 1.8rem;
    font-size: 0.75rem;
}

.btn-circle.btn-lg {
    height: 3.5rem;
    width: 3.5rem;
    font-size: 1.35rem;
}

.btn-icon-split {
    padding: 0;
    overflow: hidden;
    display: inline-flex;
    align-items: stretch;
    justify-content: center;
}

.btn-icon-split .icon {
    background: rgba(0, 0, 0, 0.15);
    display: inline-block;
    padding: 0.375rem 0.75rem;
}

.btn-icon-split .text {
    display: inline-block;
    padding: 0.375rem 0.75rem;
}

.btn-icon-split.btn-sm .icon {
    padding: 0.25rem 0.5rem;
}

.btn-icon-split.btn-sm .text {
    padding: 0.25rem 0.5rem;
}

.btn-icon-split.btn-lg .icon {
    padding: 0.5rem 1rem;
}

.btn-icon-split.btn-lg .text {
    padding: 0.5rem 1rem;
}

/* Scroll to top button */
.scroll-to-top {
    position: fixed;
    right: 1rem;
    bottom: 1rem;
    display: none;
    width: 2.75rem;
    height: 2.75rem;
    text-align: center;
    color: #fff;
    background: rgba(90, 92, 105, 0.5);
    line-height: 46px;
}

.scroll-to-top:focus, .scroll-to-top:hover {
    color: white;
}

.scroll-to-top:hover {
    background: #5a5c69;
}

.scroll-to-top i {
    font-weight: 800;
}

/* Footer */
footer.sticky-footer {
    padding: 2rem 0;
    flex-shrink: 0;
}

footer.sticky-footer .copyright {
    line-height: 1;
    font-size: 0.8rem;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 0;
    }
    
    #content-wrapper {
        margin-left: 0;
    }
    
    .sidebar.toggled {
        width: 14rem;
    }
    
    #content-wrapper.toggled {
        margin-left: 14rem;
    }
}
