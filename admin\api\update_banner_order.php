<?php
// Include init
require_once '../../includes/init.php';

// Kiểm tra quyền admin qua session
session_start();
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || $_SESSION['is_admin'] !== true) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Không có quyền truy cập'
    ]);
    exit;
}

// Kiểm tra nếu là request POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // L<PERSON>y dữ liệu từ request
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['banners']) || !is_array($input['banners'])) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Dữ liệu không hợp lệ'
        ]);
        exit;
    }
    
    // Bắt đầu transaction để đảm bảo tính toàn vẹn dữ liệu
    try {
        $conn->beginTransaction();
        
        // Cập nhật thứ tự cho từng banner
        foreach ($input['banners'] as $index => $banner) {
            if (!isset($banner['id'])) continue;
            
            $id = intval($banner['id']);
            $order = ($index + 1) * 10; // Tạo khoảng cách giữa các thứ tự 10, 20, 30...
            
            $sql = "UPDATE banners SET sort_order = :order WHERE id = :id";
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->bindParam(':order', $order, PDO::PARAM_INT);
            $stmt->execute();
        }
        
        // Hoàn tất transaction
        $conn->commit();
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'Cập nhật thứ tự banner thành công'
        ]);
        
    } catch (PDOException $e) {
        // Nếu có lỗi, rollback transaction
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        
        error_log("Lỗi khi cập nhật thứ tự banner: " . $e->getMessage());
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Lỗi khi cập nhật thứ tự banner: ' . $e->getMessage()
        ]);
    }
} else {
    // Nếu không phải POST request
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Phương thức không được hỗ trợ'
    ]);
} 