<?php
// Include init
require_once '../includes/init.php';

// <PERSON><PERSON>m tra đăng nhập và quyền admin
include_once 'partials/check_admin.php';

// X<PERSON> lý các hành động
if (isset($_GET['action'])) {
    $action = $_GET['action'];
    
    // Xử lý phê duyệt đánh giá
    if ($action === 'approve_review' && isset($_GET['id'])) {
        $review_id = intval($_GET['id']);
        $result = update_review_status($review_id, 'approved');
        
        if ($result['success']) {
            set_flash_message('success', 'Phê duyệt đánh giá thành công');
        } else {
            set_flash_message('error', $result['message']);
        }
        
        redirect(BASE_URL . '/admin/reviews.php');
    }
    
    // Xử lý từ chối đánh giá
    if ($action === 'reject_review' && isset($_GET['id'])) {
        $review_id = intval($_GET['id']);
        $result = update_review_status($review_id, 'rejected');
        
        if ($result['success']) {
            set_flash_message('success', 'Từ chối đánh giá thành công');
        } else {
            set_flash_message('error', $result['message']);
        }
        
        redirect(BASE_URL . '/admin/reviews.php');
    }
    
    // Xử lý xóa đánh giá
    if ($action === 'delete_review' && isset($_GET['id'])) {
        $review_id = intval($_GET['id']);
        $result = delete_review($review_id);
        
        if ($result['success']) {
            set_flash_message('success', 'Xóa đánh giá thành công');
        } else {
            set_flash_message('error', $result['message']);
        }
        
        redirect(BASE_URL . '/admin/reviews.php');
    }
    
    // Xử lý phê duyệt phản hồi
    if ($action === 'approve_reply' && isset($_GET['id'])) {
        $reply_id = intval($_GET['id']);
        $result = update_reply_status($reply_id, 'approved');
        
        if ($result['success']) {
            set_flash_message('success', 'Phê duyệt phản hồi thành công');
        } else {
            set_flash_message('error', $result['message']);
        }
        
        redirect(BASE_URL . '/admin/reviews.php?tab=replies');
    }
    
    // Xử lý từ chối phản hồi
    if ($action === 'reject_reply' && isset($_GET['id'])) {
        $reply_id = intval($_GET['id']);
        $result = update_reply_status($reply_id, 'rejected');
        
        if ($result['success']) {
            set_flash_message('success', 'Từ chối phản hồi thành công');
        } else {
            set_flash_message('error', $result['message']);
        }
        
        redirect(BASE_URL . '/admin/reviews.php?tab=replies');
    }
    
    // Xử lý xóa phản hồi
    if ($action === 'delete_reply' && isset($_GET['id'])) {
        $reply_id = intval($_GET['id']);
        $result = delete_reply($reply_id);
        
        if ($result['success']) {
            set_flash_message('success', 'Xóa phản hồi thành công');
        } else {
            set_flash_message('error', $result['message']);
        }
        
        redirect(BASE_URL . '/admin/reviews.php?tab=replies');
    }
}

// Lấy tab hiện tại
$current_tab = isset($_GET['tab']) ? $_GET['tab'] : 'reviews';

// Lấy trang hiện tại
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;

// Lấy danh sách đánh giá chờ duyệt
$reviews_data = get_pending_reviews($page);

// Lấy danh sách phản hồi chờ duyệt
$replies_data = get_pending_replies($page);

// Thiết lập tiêu đề trang
$page_title = 'Quản lý đánh giá';

// Include header
include_once 'partials/header.php';
?>

<!-- Begin Page Content -->
<div class="container-fluid">

    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Quản lý đánh giá</h1>
    </div>

    <!-- Tabs -->
    <ul class="nav nav-tabs mb-4">
        <li class="nav-item">
            <a class="nav-link <?php echo $current_tab === 'reviews' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/admin/reviews.php?tab=reviews">
                Đánh giá chờ duyệt
                <?php if (count($reviews_data['reviews']) > 0): ?>
                <span class="badge badge-pill badge-primary ml-1"><?php echo count($reviews_data['reviews']); ?></span>
                <?php endif; ?>
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo $current_tab === 'replies' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>/admin/reviews.php?tab=replies">
                Phản hồi chờ duyệt
                <?php if (count($replies_data['replies']) > 0): ?>
                <span class="badge badge-pill badge-primary ml-1"><?php echo count($replies_data['replies']); ?></span>
                <?php endif; ?>
            </a>
        </li>
    </ul>

    <?php display_flash_message(); ?>

    <?php if ($current_tab === 'reviews'): ?>
    <!-- Đánh giá chờ duyệt -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Đánh giá chờ duyệt</h6>
        </div>
        <div class="card-body">
            <?php if (count($reviews_data['reviews']) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Sản phẩm</th>
                                <th>Người đánh giá</th>
                                <th>Đánh giá</th>
                                <th>Nội dung</th>
                                <th>Ngày tạo</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($reviews_data['reviews'] as $review): ?>
                                <tr>
                                    <td><?php echo $review['id']; ?></td>
                                    <td>
                                        <a href="<?php echo BASE_URL; ?>/product.php?slug=<?php echo $review['product_slug']; ?>" target="_blank">
                                            <?php echo $review['product_name']; ?>
                                        </a>
                                    </td>
                                    <td>
                                        <?php if ($review['user_id']): ?>
                                            <?php echo $review['full_name']; ?><br>
                                            <small class="text-muted"><?php echo $review['email']; ?></small>
                                        <?php else: ?>
                                            <?php echo $review['guest_name']; ?><br>
                                            <small class="text-muted"><?php echo $review['guest_email']; ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($review['rating']): ?>
                                            <div class="text-warning">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <?php if ($i <= $review['rating']): ?>
                                                        <i class="fas fa-star"></i>
                                                    <?php else: ?>
                                                        <i class="far fa-star"></i>
                                                    <?php endif; ?>
                                                <?php endfor; ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">Không có đánh giá</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($review['review_title']): ?>
                                            <strong><?php echo htmlspecialchars($review['review_title']); ?></strong><br>
                                        <?php endif; ?>
                                        <?php echo nl2br(htmlspecialchars($review['review_content'])); ?>
                                        
                                        <?php if (!empty($review['media'])): ?>
                                            <div class="mt-2">
                                                <small class="text-muted">Có <?php echo count($review['media']); ?> file đính kèm</small>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($review['created_at'])); ?></td>
                                    <td>
                                        <a href="<?php echo BASE_URL; ?>/admin/reviews.php?action=approve_review&id=<?php echo $review['id']; ?>" class="btn btn-success btn-sm mb-1">
                                            <i class="fas fa-check"></i> Phê duyệt
                                        </a>
                                        <a href="<?php echo BASE_URL; ?>/admin/reviews.php?action=reject_review&id=<?php echo $review['id']; ?>" class="btn btn-warning btn-sm mb-1">
                                            <i class="fas fa-ban"></i> Từ chối
                                        </a>
                                        <a href="<?php echo BASE_URL; ?>/admin/reviews.php?action=delete_review&id=<?php echo $review['id']; ?>" class="btn btn-danger btn-sm mb-1" onclick="return confirm('Bạn có chắc chắn muốn xóa đánh giá này?');">
                                            <i class="fas fa-trash"></i> Xóa
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php if ($reviews_data['total_pages'] > 1): ?>
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center mt-4">
                            <?php for ($i = 1; $i <= $reviews_data['total_pages']; $i++): ?>
                                <li class="page-item <?php echo $i === $reviews_data['current_page'] ? 'active' : ''; ?>">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/admin/reviews.php?tab=reviews&page=<?php echo $i; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-4">
                    <p class="text-muted">Không có đánh giá nào chờ duyệt.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <?php else: ?>
    <!-- Phản hồi chờ duyệt -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Phản hồi chờ duyệt</h6>
        </div>
        <div class="card-body">
            <?php if (count($replies_data['replies']) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Sản phẩm</th>
                                <th>Người phản hồi</th>
                                <th>Nội dung</th>
                                <th>Ngày tạo</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($replies_data['replies'] as $reply): ?>
                                <tr>
                                    <td><?php echo $reply['id']; ?></td>
                                    <td>
                                        <a href="<?php echo BASE_URL; ?>/product.php?slug=<?php echo $reply['product_slug']; ?>" target="_blank">
                                            <?php echo $reply['product_name']; ?>
                                        </a>
                                    </td>
                                    <td>
                                        <?php if ($reply['user_id']): ?>
                                            <?php echo $reply['full_name']; ?><br>
                                            <small class="text-muted"><?php echo $reply['email']; ?></small>
                                        <?php else: ?>
                                            <?php echo $reply['guest_name']; ?><br>
                                            <small class="text-muted"><?php echo $reply['guest_email']; ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo nl2br(htmlspecialchars($reply['reply_content'])); ?>
                                        
                                        <?php if (!empty($reply['media'])): ?>
                                            <div class="mt-2">
                                                <small class="text-muted">Có <?php echo count($reply['media']); ?> file đính kèm</small>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($reply['created_at'])); ?></td>
                                    <td>
                                        <a href="<?php echo BASE_URL; ?>/admin/reviews.php?action=approve_reply&id=<?php echo $reply['id']; ?>" class="btn btn-success btn-sm mb-1">
                                            <i class="fas fa-check"></i> Phê duyệt
                                        </a>
                                        <a href="<?php echo BASE_URL; ?>/admin/reviews.php?action=reject_reply&id=<?php echo $reply['id']; ?>" class="btn btn-warning btn-sm mb-1">
                                            <i class="fas fa-ban"></i> Từ chối
                                        </a>
                                        <a href="<?php echo BASE_URL; ?>/admin/reviews.php?action=delete_reply&id=<?php echo $reply['id']; ?>" class="btn btn-danger btn-sm mb-1" onclick="return confirm('Bạn có chắc chắn muốn xóa phản hồi này?');">
                                            <i class="fas fa-trash"></i> Xóa
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php if ($replies_data['total_pages'] > 1): ?>
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center mt-4">
                            <?php for ($i = 1; $i <= $replies_data['total_pages']; $i++): ?>
                                <li class="page-item <?php echo $i === $replies_data['current_page'] ? 'active' : ''; ?>">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/admin/reviews.php?tab=replies&page=<?php echo $i; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-4">
                    <p class="text-muted">Không có phản hồi nào chờ duyệt.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

</div>
<!-- /.container-fluid -->

<?php
// Include footer
include_once 'partials/footer.php';
?>
