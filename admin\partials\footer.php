                </div>
                <!-- /.container-fluid -->
            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>Copyright &copy; <?php echo SITE_NAME; ?> <?php echo date('Y'); ?></span>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->
        </div>
        <!-- End of Content Wrapper -->
    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Bootstrap core JavaScript-->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Select2 JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- CustomEditor - Trình soạn thảo tùy chỉnh -->
    <script src="<?php echo BASE_URL; ?>/admin/assets/js/custom-editor.js"></script>

    <!-- SimpleEditor - Trình soạn thảo đơn giản -->
    <script src="<?php echo BASE_URL; ?>/admin/assets/js/simple-editor.js"></script>

    <!-- Summernote JS -->
    <script src="<?php echo BASE_URL; ?>/summernote-0.9.0-dist/summernote.min.js"></script>

    <!-- Summernote Language (Tiếng Việt) -->
    <script src="<?php echo BASE_URL; ?>/summernote-0.9.0-dist/lang/summernote-vi-VN.min.js"></script>

    <!-- Summernote Config -->
    <script>
        // Định nghĩa biến BASE_URL cho JavaScript
        var BASE_URL = '<?php echo BASE_URL; ?>';
    </script>
    <script src="<?php echo BASE_URL; ?>/admin/assets/js/summernote-config.js"></script>

    <?php
    $current_script = basename($_SERVER['SCRIPT_FILENAME']);
    if ($current_script == 'product-add.php' || $current_script == 'product-edit.php'):
    ?>
    <!-- Product Details CSS & JS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/admin/assets/css/product-details.css">
    <script src="<?php echo BASE_URL; ?>/admin/assets/js/product-details.js"></script>
    <?php endif; ?>

    <!-- Custom scripts for all pages-->
    <script src="<?php echo BASE_URL; ?>/admin/assets/js/admin.js"></script>
    <!-- Modern Admin JavaScript -->
    <script src="<?php echo BASE_URL; ?>/admin/assets/js/admin-modern.js"></script>

    <!-- Center Notifications System -->
    <script src="<?php echo BASE_URL; ?>/assets/js/center-notifications.js"></script>

    <!-- Unified Notifications System for Admin -->
    <script src="<?php echo BASE_URL; ?>/admin/assets/js/unified-notifications.js"></script>

    <!-- Khởi tạo CustomEditor cho trang thêm/sửa sản phẩm -->
    <?php
    if ($current_script == 'product-add.php' || $current_script == 'product-edit.php'):
    ?>
    <script>
        // Khởi tạo CustomEditor khi trang đã tải xong
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, checking for product_content_editor');

            // Tìm textarea có id là product_content_editor
            var contentTextarea = document.getElementById('product_content_editor');
            console.log('Content textarea found:', contentTextarea);

            // Tìm textarea có id là overview
            var overviewTextarea = document.getElementById('overview');
            console.log('Overview textarea found:', overviewTextarea);

            // Khởi tạo SimpleEditor cho phần tổng quan sản phẩm
            if (overviewTextarea) {
                console.log('Found overview textarea, initializing SimpleEditor');

                // Khởi tạo SimpleEditor cho phần tổng quan
                window.overviewEditor = new SimpleEditor({
                    element: overviewTextarea,
                    height: '300px',
                    placeholder: 'Nhập tổng quan về sản phẩm...',
                    uploadUrl: '<?php echo BASE_URL; ?>/admin/upload-image.php',
                    value: overviewTextarea.value,
                    buttons: [
                        'bold', 'italic', 'underline',
                        'alignLeft', 'alignCenter', 'alignRight', 'alignJustify',
                        'bulletList', 'numberedList',
                        'fontSize', 'fontColor',
                        'image', 'clear'
                    ]
                });

                console.log('SimpleEditor initialized for overview');
            }

            // Khởi tạo CustomEditor cho các textarea
            if (contentTextarea) {
                console.log('Found product_content_editor, initializing CustomEditor');

                // Khởi tạo CustomEditor cho nội dung chi tiết
                window.productContentEditor = new CustomEditor({
                    element: contentTextarea,
                    height: '400px',
                    placeholder: 'Nhập nội dung chi tiết sản phẩm...',
                    uploadUrl: '<?php echo BASE_URL; ?>/admin/ckeditor-upload.php',
                    value: contentTextarea.value,
                    onChange: function(content) {
                        console.log('Content changed');
                    }
                });

                console.log('CustomEditor initialized for product content');

                // Khởi tạo CustomEditor cho thông số kỹ thuật nếu có
                var specificationsTextarea = document.getElementById('specifications_editor');
                if (specificationsTextarea) {
                    window.specificationsEditor = new CustomEditor({
                        element: specificationsTextarea,
                        height: '300px',
                        placeholder: 'Nhập thông số kỹ thuật sản phẩm...',
                        uploadUrl: '<?php echo BASE_URL; ?>/admin/ckeditor-upload.php',
                        value: specificationsTextarea.value
                    });
                    console.log('CustomEditor initialized for specifications');
                }

                // Khởi tạo CustomEditor cho hướng dẫn sử dụng nếu có
                var usageGuideTextarea = document.getElementById('usage_guide_editor');
                if (usageGuideTextarea) {
                    window.usageGuideEditor = new CustomEditor({
                        element: usageGuideTextarea,
                        height: '300px',
                        placeholder: 'Nhập hướng dẫn sử dụng sản phẩm...',
                        uploadUrl: '<?php echo BASE_URL; ?>/admin/ckeditor-upload.php',
                        value: usageGuideTextarea.value
                    });
                    console.log('CustomEditor initialized for usage guide');
                }

                // Xử lý các nút mẫu nội dung
                const templateButtons = document.querySelectorAll('.template-btn');
                templateButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const templateType = this.getAttribute('data-template');
                        let templateContent = '';

                        // Lấy nội dung mẫu tương ứng
                        switch(templateType) {
                            case 'basic':
                                templateContent = getBasicTemplate();
                                break;
                            case 'detailed':
                                templateContent = getDetailedTemplate();
                                break;
                            case 'specifications':
                                templateContent = getSpecificationsTemplate();
                                break;
                            case 'maintenance':
                                templateContent = getMaintenanceTemplate();
                                break;
                        }

                        // Xác nhận từ người dùng trước khi chèn
                        if (confirm('Nội dung hiện tại sẽ bị thay thế bằng mẫu được chọn. Bạn có chắc chắn muốn tiếp tục?')) {
                            // Thiết lập nội dung cho CustomEditor
                            if (window.productContentEditor) {
                                window.productContentEditor.setContent(templateContent);
                                console.log('Template content set successfully');
                            }
                        }
                    });
                });

                // Các hàm trả về mẫu nội dung
                function getBasicTemplate() {
                    return `
<h2>Thông tin sản phẩm</h2>
<p>Mô tả ngắn gọn về sản phẩm và các đặc điểm nổi bật.</p>

<h3>Đặc điểm nổi bật</h3>
<ul>
    <li>Đặc điểm 1</li>
    <li>Đặc điểm 2</li>
    <li>Đặc điểm 3</li>
</ul>

<h3>Thông số kỹ thuật</h3>
<ul>
    <li><strong>Chất liệu:</strong> [Điền chất liệu]</li>
    <li><strong>Kích thước:</strong> [Điền kích thước]</li>
    <li><strong>Màu sắc:</strong> [Điền màu sắc]</li>
    <li><strong>Xuất xứ:</strong> [Điền xuất xứ]</li>
</ul>

<h3>Bảo hành</h3>
<p>Thông tin về chính sách bảo hành của sản phẩm.</p>`;
                }

                function getDetailedTemplate() {
                    return `
<h2>Giới thiệu sản phẩm</h2>
<p>Mô tả chi tiết về sản phẩm, nguồn gốc, ý tưởng thiết kế và các đặc điểm nổi bật.</p>

<h3>Thiết kế và phong cách</h3>
<p>Mô tả về thiết kế, phong cách và cách sản phẩm phù hợp với không gian sống.</p>

<h3>Chất liệu cao cấp</h3>
<p>Thông tin chi tiết về chất liệu được sử dụng, nguồn gốc và đặc tính của chất liệu.</p>

<h3>Kích thước và không gian phù hợp</h3>
<p>Mô tả về kích thước sản phẩm và các không gian phù hợp để đặt sản phẩm.</p>

<h3>Công năng sử dụng</h3>
<p>Thông tin về công năng sử dụng của sản phẩm và các tính năng đặc biệt.</p>

<h3>Hướng dẫn lắp đặt và sử dụng</h3>
<p>Hướng dẫn chi tiết về cách lắp đặt và sử dụng sản phẩm.</p>

<h3>Bảo quản và vệ sinh</h3>
<p>Hướng dẫn cách bảo quản và vệ sinh sản phẩm để kéo dài tuổi thọ.</p>

<h3>Chính sách bảo hành</h3>
<p>Thông tin chi tiết về chính sách bảo hành của sản phẩm.</p>`;
                }

                function getSpecificationsTemplate() {
                    return `
<h2>Thông số kỹ thuật</h2>
<p>Dưới đây là thông số kỹ thuật chi tiết của sản phẩm:</p>

<table class="simple-editor-table">
    <thead>
        <tr>
            <th style="width:30%">Thông số</th>
            <th>Chi tiết</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td><strong>Tên sản phẩm</strong></td>
            <td>[Điền tên sản phẩm]</td>
        </tr>
        <tr>
            <td><strong>Mã sản phẩm</strong></td>
            <td>[Điền mã sản phẩm]</td>
        </tr>
        <tr>
            <td><strong>Chất liệu</strong></td>
            <td>[Điền chất liệu]</td>
        </tr>
        <tr>
            <td><strong>Kích thước tổng thể</strong></td>
            <td>[Điền kích thước tổng thể]</td>
        </tr>
        <tr>
            <td><strong>Kích thước chi tiết</strong></td>
            <td>
                - Chiều dài: [Điền chiều dài]<br>
                - Chiều rộng: [Điền chiều rộng]<br>
                - Chiều cao: [Điền chiều cao]
            </td>
        </tr>
        <tr>
            <td><strong>Màu sắc</strong></td>
            <td>[Điền màu sắc]</td>
        </tr>
        <tr>
            <td><strong>Trọng lượng</strong></td>
            <td>[Điền trọng lượng]</td>
        </tr>
        <tr>
            <td><strong>Xuất xứ</strong></td>
            <td>[Điền xuất xứ]</td>
        </tr>
        <tr>
            <td><strong>Thời gian bảo hành</strong></td>
            <td>[Điền thời gian bảo hành]</td>
        </tr>
    </tbody>
</table>`;
                }

                function getMaintenanceTemplate() {
                    return `
<h2>Hướng dẫn bảo quản và vệ sinh</h2>
<p>Để sản phẩm luôn bền đẹp và kéo dài tuổi thọ, vui lòng tham khảo các hướng dẫn bảo quản dưới đây:</p>

<h3>Hướng dẫn chung</h3>
<ul>
    <li>Tránh đặt sản phẩm dưới ánh nắng trực tiếp để tránh phai màu và biến dạng.</li>
    <li>Đặt sản phẩm ở nơi khô ráo, thoáng mát, tránh nơi ẩm ướt.</li>
    <li>Tránh va đập mạnh vào sản phẩm, đặc biệt là các góc cạnh.</li>
    <li>Không kéo lê sản phẩm trên sàn để tránh hư hỏng chân đế.</li>
</ul>

<h3>Vệ sinh thường xuyên</h3>
<ol>
    <li>Lau chùi bụi bẩn thường xuyên bằng khăn mềm, khô.</li>
    <li>Đối với vết bẩn nhẹ, sử dụng khăn ẩm và lau nhẹ nhàng.</li>
    <li>Đối với vết bẩn cứng đầu, sử dụng dung dịch vệ sinh chuyên dụng cho loại chất liệu của sản phẩm.</li>
    <li>Sau khi lau bằng khăn ẩm, cần lau lại bằng khăn khô để tránh ẩm ướt.</li>
</ol>

<h3>Bảo quản theo chất liệu</h3>

<h4>Đối với sản phẩm gỗ</h4>
<ul>
    <li>Tránh để nước đọng trên bề mặt sản phẩm.</li>
    <li>Sử dụng sáp đánh bóng gỗ định kỳ 3-6 tháng một lần.</li>
    <li>Tránh sử dụng các chất tẩy rửa có tính axit hoặc kiềm mạnh.</li>
</ul>

<h4>Đối với sản phẩm da</h4>
<ul>
    <li>Vệ sinh bằng khăn mềm và sản phẩm chuyên dụng cho đồ da.</li>
    <li>Tránh sử dụng các chất tẩy rửa thông thường.</li>
    <li>Thoa kem dưỡng da định kỳ để giữ độ ẩm và độ đàn hồi.</li>
</ul>`;
                }
            } else {
                console.log('Content textarea not found');
            }
        });
    </script>
    <?php endif; ?>

    <!-- Initialize components -->
    <script>
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            // Bootstrap 4 dropdowns are initialized automatically with jQuery
            // No need for manual initialization

            // Toggle sidebar
            var sidebarToggle = document.getElementById('sidebarToggle');
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.querySelector('body').classList.toggle('sidebar-toggled');
                    document.querySelector('.sidebar').classList.toggle('toggled');
                    document.querySelector('#content-wrapper').classList.toggle('toggled');
                });
            }

            // Initialize Select2
            if ($.fn.select2) {
                $('.select2').select2({
                    width: '100%',
                    placeholder: 'Chọn một mục',
                    allowClear: true
                });
            }
        });
    </script>
</body>
</html>
