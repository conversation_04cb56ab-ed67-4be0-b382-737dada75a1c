<?php
/**
 * Cập nhật cài đặt footer
 * File này sẽ tự động cập nhật cơ sở dữ liệu với các cài đặt footer
 */

// Include các file cần thiết
require_once '../includes/init.php';

// Thiết lập tiêu đề trang
$page_title = 'Cập nhật cài đặt Footer';

// Khởi tạo biến
$success_messages = [];
$error_messages = [];

// Hàm để thực thi câu lệnh SQL an toàn
function execute_sql($conn, $sql) {
    try {
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        return true;
    } catch (PDOException $e) {
        global $error_messages;
        $error_messages[] = "Lỗi SQL: " . $e->getMessage();
        return false;
    }
}

// Bắt đầu cập nhật
try {
    // Bắt đầu transaction
    $conn->beginTransaction();

    // Tạo bảng site_settings nếu chưa tồn tại
    $create_table_sql = "CREATE TABLE IF NOT EXISTS site_settings (
        id INT(11) NOT NULL AUTO_INCREMENT,
        setting_key VARCHAR(255) NOT NULL,
        setting_value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY (setting_key)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    if (execute_sql($conn, $create_table_sql)) {
        $success_messages[] = "Đã tạo bảng site_settings thành công (nếu chưa tồn tại).";
    }

    // Mảng các cài đặt cần thêm
    $settings = [
        'footer_col1_content' => 'Nội Thất Chất Lượng Hà Nội cung cấp các sản phẩm nội thất cao cấp, chất lượng với giá thành hợp lý. Chúng tôi tự hào mang đến những sản phẩm đẹp, bền và phù hợp với mọi không gian sống.',
        'company_name' => 'Công ty TNHH Nội Thất Chất Lượng Hà Nội',
        'company_address' => '123 Đường ABC, Quận XYZ, Hà Nội',
        'company_phone' => '0123.456.789',
        'company_email' => '<EMAIL>',
        'business_hours' => 'Thứ 2 - Chủ nhật: 8:00 - 20:00',
        'footer_col2_title' => 'Liên kết nhanh',
        'footer_col3_title' => 'Hỗ trợ khách hàng',
        'footer_col4_title' => 'Kết nối với chúng tôi',
        'footer_copyright_text' => '© ' . date('Y') . ' Công ty TNHH Nội Thất Chất Lượng Hà Nội. Tất cả quyền được bảo lưu.',
        'facebook_url' => 'https://web.facebook.com/tunhuachatluong',
        'instagram_url' => 'https://instagram.com/noithatbangvu',
        'youtube_url' => 'https://youtube.com/noithatbangvu',
        'tiktok_url' => 'https://tiktok.com/@noithatbangvu',
        'zalo_url' => 'https://zalo.me/noithatbangvu'
    ];

    // Thêm các cài đặt cơ bản
    foreach ($settings as $key => $value) {
        $sql = "INSERT INTO site_settings (setting_key, setting_value) VALUES (?, ?)
                ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$key, $value]);
        $success_messages[] = "Đã cập nhật cài đặt: $key";
    }

    // Thêm danh sách chứng nhận mặc định
    $certifications = [
        [
            'name' => 'Bộ Công Thương',
            'url' => 'http://online.gov.vn/',
            'image' => '/uploads/footer/bocongthong.png'
        ]
    ];

    $certifications_json = json_encode($certifications, JSON_UNESCAPED_UNICODE);
    $sql = "INSERT INTO site_settings (setting_key, setting_value) VALUES (?, ?)
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
    $stmt = $conn->prepare($sql);
    $stmt->execute(['footer_certifications', $certifications_json]);
    $success_messages[] = "Đã cập nhật danh sách chứng nhận.";

    // Thêm liên kết nhanh
    $quick_links = [
        ['text' => 'Trang chủ', 'url' => '/'],
        ['text' => 'Giới thiệu', 'url' => '/about.php'],
        ['text' => 'Sản phẩm', 'url' => '/products.php'],
        ['text' => 'Tin tức', 'url' => '/blog.php'],
        ['text' => 'Liên hệ', 'url' => '/contact.php']
    ];

    $quick_links_json = json_encode($quick_links, JSON_UNESCAPED_UNICODE);
    $sql = "INSERT INTO site_settings (setting_key, setting_value) VALUES (?, ?)
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
    $stmt = $conn->prepare($sql);
    $stmt->execute(['footer_col2_links', $quick_links_json]);
    $success_messages[] = "Đã cập nhật liên kết nhanh.";

    // Thêm liên kết hỗ trợ khách hàng
    $support_links = [
        ['text' => 'Đổi trả bảo hành', 'url' => '/warranty.php'],
        ['text' => 'Hình thức thanh toán', 'url' => '/payment.php'],
        ['text' => 'Vận chuyển giao hàng', 'url' => '/shipping.php'],
        ['text' => 'Chính sách bảo mật', 'url' => '/privacy-policy.php'],
        ['text' => 'Điều khoản sử dụng', 'url' => '/terms-of-service.php']
    ];

    $support_links_json = json_encode($support_links, JSON_UNESCAPED_UNICODE);
    $sql = "INSERT INTO site_settings (setting_key, setting_value) VALUES (?, ?)
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
    $stmt = $conn->prepare($sql);
    $stmt->execute(['footer_col3_links', $support_links_json]);
    $success_messages[] = "Đã cập nhật liên kết hỗ trợ khách hàng.";

    // Commit transaction
    $conn->commit();
    $success_messages[] = "Cập nhật cài đặt footer thành công!";

} catch (Exception $e) {
    // Rollback transaction nếu có lỗi và nếu có giao dịch đang hoạt động
    try {
        if ($conn && $conn->inTransaction()) {
            $conn->rollBack();
        }
    } catch (PDOException $rollbackException) {
        $error_messages[] = "Lỗi khi rollback: " . $rollbackException->getMessage();
    }

    $error_messages[] = "Đã xảy ra lỗi: " . $e->getMessage();
}

// Include header
include_once 'partials/header.php';
?>

<!-- Content -->
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800"><?php echo $page_title; ?></h1>

    <?php if (!empty($error_messages)): ?>
        <div class="alert alert-danger">
            <h4 class="alert-heading">Lỗi!</h4>
            <ul>
                <?php foreach ($error_messages as $message): ?>
                    <li><?php echo $message; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <?php if (!empty($success_messages)): ?>
        <div class="alert alert-success">
            <h4 class="alert-heading">Thành công!</h4>
            <p>Đã cập nhật cài đặt footer thành công. Dưới đây là chi tiết:</p>
            <ul>
                <?php foreach ($success_messages as $message): ?>
                    <li><?php echo $message; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Các bước tiếp theo</h6>
        </div>
        <div class="card-body">
            <ol>
                <li>Truy cập vào "Quản lý Footer" từ sidebar</li>
                <li>Chỉnh sửa thông tin footer theo ý muốn</li>
                <li>Lưu thay đổi và kiểm tra trên trang chủ</li>
            </ol>

            <div class="mt-4">
                <a href="<?php echo BASE_URL; ?>/admin/footer-settings.php" class="btn btn-primary">
                    <i class="fas fa-cog"></i> Đến trang Quản lý Footer
                </a>
                <a href="<?php echo BASE_URL; ?>" class="btn btn-success ml-2" target="_blank">
                    <i class="fas fa-home"></i> Xem trang chủ
                </a>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
include_once 'partials/footer.php';
?>
