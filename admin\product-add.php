<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra đăng nhập và quyền admin
if (!is_admin_logged_in() || !check_admin_role()) {
    set_flash_message('error', 'Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/admin/login.php');
}

// Thiết lập tiêu đề trang
$page_title = 'Thêm sản phẩm mới';

// Lấy danh sách danh mục
$categories = get_categories();

// X<PERSON> lý form thêm sản phẩm
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $sku = trim($_POST['sku'] ?? '');
    $category_id = intval($_POST['category_id'] ?? 0);
    $description = trim($_POST['description'] ?? '');
    $content = trim($_POST['content'] ?? '');

    // Dữ liệu mô tả chi tiết mới
    $overview = trim($_POST['overview'] ?? '');

    // Xử lý đặc điểm nổi bật
    $features = isset($_POST['features']) ? array_filter($_POST['features'], function($item) {
        return !empty(trim($item));
    }) : [];

    // Xử lý thông số kỹ thuật
    $specifications_json = [];
    if (isset($_POST['spec_keys']) && isset($_POST['spec_values'])) {
        $spec_count = min(count($_POST['spec_keys']), count($_POST['spec_values']));
        for ($i = 0; $i < $spec_count; $i++) {
            $key = trim($_POST['spec_keys'][$i]);
            $value = trim($_POST['spec_values'][$i]);
            if (!empty($key)) {
                $specifications_json[$key] = $value;
            }
        }
    }

    // Xử lý hướng dẫn sử dụng
    $usage_guide_json = [
        'steps' => isset($_POST['usage_steps']) ? array_filter($_POST['usage_steps'], function($item) {
            return !empty(trim($item));
        }) : [],
        'notes' => isset($_POST['usage_notes']) ? array_filter($_POST['usage_notes'], function($item) {
            return !empty(trim($item));
        }) : []
    ];

    // Xử lý thông tin bảo hành
    $warranty_info = [
        'time' => trim($_POST['warranty_time'] ?? '12 tháng'),
        'scope' => trim($_POST['warranty_scope'] ?? 'Bảo hành toàn bộ sản phẩm'),
        'conditions' => isset($_POST['warranty_conditions']) ? array_filter($_POST['warranty_conditions'], function($item) {
            return !empty(trim($item));
        }) : [],
        'contact' => trim($_POST['warranty_contact'] ?? 'Hotline: 1900.1234 - Email: <EMAIL>')
    ];

    $price = floatval($_POST['price'] ?? 0);
    $sale_price = !empty($_POST['sale_price']) ? floatval($_POST['sale_price']) : null;
    $quantity = intval($_POST['quantity'] ?? 0);
    $status = isset($_POST['status']) ? 1 : 0;
    $featured = isset($_POST['featured']) ? 1 : 0;
    $rating = intval($_POST['rating'] ?? 5);
    $sold = intval($_POST['sold'] ?? 0);
    $views = intval($_POST['views'] ?? 0);
    $flash_sale = isset($_POST['flash_sale']) ? 1 : 0;
    $show_on_homepage = isset($_POST['show_on_homepage']) ? 1 : 0;
    $material = trim($_POST['material'] ?? '');
    $dimensions = trim($_POST['dimensions'] ?? '');
    $color = trim($_POST['color'] ?? '');
    $price_type = $_POST['price_type'] ?? 'fixed';

    // Xử lý tùy chọn kích thước và giá
    $size_options = [];
    if (isset($_POST['size_option']) && is_array($_POST['size_option']) &&
        isset($_POST['size_price']) && is_array($_POST['size_price'])) {

        $size_count = count($_POST['size_option']);
        for ($i = 0; $i < $size_count; $i++) {
            if (!empty($_POST['size_option'][$i]) && isset($_POST['size_price'][$i])) {
                $size_options[] = [
                    'size' => $_POST['size_option'][$i],
                    'price' => floatval($_POST['size_price'][$i])
                ];
            }
        }
    }

    $meta_title = trim($_POST['meta_title'] ?? '');
    $meta_description = trim($_POST['meta_description'] ?? '');

    // Validate dữ liệu
    $errors = [];

    if (empty($name)) {
        $errors[] = 'Tên sản phẩm không được để trống';
    }

    if ($category_id <= 0) {
        $errors[] = 'Vui lòng chọn danh mục';
    }

    if ($price <= 0) {
        $errors[] = 'Giá sản phẩm phải lớn hơn 0';
    }

    if ($sale_price !== null && $sale_price >= $price) {
        $errors[] = 'Giá khuyến mãi phải nhỏ hơn giá gốc';
    }

    if ($quantity < 0) {
        $errors[] = 'Số lượng không được âm';
    }

    // Xử lý upload hình ảnh chính
    $image = '';
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../uploads/products/';

        // Tạo thư mục uploads/products nếu chưa tồn tại
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        $file_name = $_FILES['image']['name'];
        $file_tmp = $_FILES['image']['tmp_name'];
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

        // Kiểm tra định dạng file
        $allowed_exts = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        if (!in_array($file_ext, $allowed_exts)) {
            $errors[] = 'Chỉ cho phép upload file hình ảnh (jpg, jpeg, png, gif, webp)';
        } else {
            // Tạo tên file mới để tránh trùng lặp
            $new_file_name = 'product_' . time() . '_' . uniqid() . '.' . $file_ext;
            $upload_path = $upload_dir . $new_file_name;

            if (move_uploaded_file($file_tmp, $upload_path)) {
                $image = $new_file_name;
            } else {
                $errors[] = 'Có lỗi xảy ra khi upload file';
            }
        }
    }

    // Xử lý upload hình ảnh phụ (gallery)
    $gallery = [];
    if (isset($_FILES['gallery']) && is_array($_FILES['gallery']['name'])) {
        $upload_dir = '../uploads/products/';

        // Tạo thư mục uploads/products nếu chưa tồn tại
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        // Lấy số lượng hình ảnh phụ
        $gallery_count = count($_FILES['gallery']['name']);

        for ($i = 0; $i < $gallery_count; $i++) {
            if ($_FILES['gallery']['error'][$i] === UPLOAD_ERR_OK) {
                $file_name = $_FILES['gallery']['name'][$i];
                $file_tmp = $_FILES['gallery']['tmp_name'][$i];
                $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

                // Kiểm tra định dạng file
                $allowed_exts = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                if (!in_array($file_ext, $allowed_exts)) {
                    $errors[] = 'Hình ảnh phụ "' . $file_name . '" không đúng định dạng. Chỉ cho phép upload file hình ảnh (jpg, jpeg, png, gif, webp)';
                } else {
                    // Tạo tên file mới để tránh trùng lặp
                    $new_file_name = 'gallery_' . time() . '_' . uniqid() . '_' . $i . '.' . $file_ext;
                    $upload_path = $upload_dir . $new_file_name;

                    if (move_uploaded_file($file_tmp, $upload_path)) {
                        $gallery[] = $new_file_name;
                    } else {
                        $errors[] = 'Có lỗi xảy ra khi upload hình ảnh phụ';
                    }
                }
            }
        }
    }

    // Nếu không có lỗi, thêm sản phẩm vào database
    if (empty($errors)) {
        // Chuyển đổi mảng gallery thành chuỗi phân cách bằng dấu phẩy
        $gallery_string = !empty($gallery) ? implode(',', $gallery) : '';

        // Chuyển đổi mảng size_options thành chuỗi JSON
        $size_options_json = !empty($size_options) ? json_encode(['options' => $size_options]) : null;

        $product_data = [
            'category_id' => $category_id,
            'name' => $name,
            'sku' => $sku,
            'description' => $description,
            'content' => $content,
            'price' => $price,
            'sale_price' => $sale_price,
            'image' => $image,
            'gallery' => $gallery_string,
            'quantity' => $quantity,
            'status' => $status,
            'featured' => $featured,
            'rating' => $rating,
            'sold' => $sold,
            'views' => $views,
            'flash_sale' => $flash_sale,
            'show_on_homepage' => $show_on_homepage,
            'material' => $material,
            'dimensions' => $dimensions,
            'color' => $color,
            'price_type' => $price_type,
            'size_options' => $size_options_json,
            'meta_title' => $meta_title,
            'meta_description' => $meta_description,
            // Dữ liệu mô tả chi tiết mới
            'overview' => $overview,
            'features' => !empty($features) ? json_encode($features, JSON_UNESCAPED_UNICODE) : null,
            'specifications_json' => !empty($specifications_json) ? json_encode($specifications_json, JSON_UNESCAPED_UNICODE) : null,
            'usage_guide_json' => !empty($usage_guide_json) ? json_encode($usage_guide_json, JSON_UNESCAPED_UNICODE) : null,
            'warranty_info' => !empty($warranty_info) ? json_encode($warranty_info, JSON_UNESCAPED_UNICODE) : null
        ];

        $result = add_product($product_data);

        if ($result['success']) {
            set_flash_message('success', $result['message']);
            // Sử dụng $_SESSION['last_products_query'] để redirect
            $redirect_url = 'products.php';
            $query_params = [];
            if (isset($_SESSION['last_products_query']) && !empty($_SESSION['last_products_query'])) {
                parse_str($_SESSION['last_products_query'], $query_params);
            }
            // Thêm auto_scroll parameter để trigger auto-scroll
            $query_params['auto_scroll'] = '1';
            $redirect_url .= '?' . http_build_query($query_params);
            redirect($redirect_url);
        } else {
            $errors[] = $result['message'];
        }
    }
}

// Include header
include_once 'partials/header.php';

// Thêm CSRF token cho AJAX
$csrf_token = generate_csrf_token();
?>

<!-- Content -->
<div class="container-fluid">
    <!-- CSRF Token cho AJAX -->
    <meta name="csrf-token" content="<?php echo $csrf_token; ?>">

    <!-- Container cho thông báo AJAX -->
    <div class="notification-container"></div>

    <!-- Modern Header -->
    <div class="product-add-header">
        <div class="product-add-header-content">
            <div class="product-add-title-section">
                <div class="product-add-icon">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="product-add-title-text">
                    <h1>Thêm sản phẩm mới</h1>
                    <p class="product-add-subtitle">Tạo sản phẩm mới cho cửa hàng nội thất</p>
                </div>
            </div>
            <div class="product-add-actions">
                <a href="products.php" class="btn-modern-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Quay lại
                </a>
            </div>
        </div>
    </div>

    <!-- Thông báo lỗi -->
    <?php if (!empty($errors)): ?>
    <div class="modern-alert modern-alert-danger">
        <div class="modern-alert-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="modern-alert-content">
            <h4>Có lỗi xảy ra</h4>
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
    <?php endif; ?>

    <!-- Form thêm sản phẩm -->
    <form method="POST" action="product-add.php" enctype="multipart/form-data" id="add-product-form">
        <div class="row">
            <div class="col-lg-8">
                <!-- Card thông tin cơ bản -->
                <div class="modern-card">
                    <div class="modern-card-header">
                        <div class="modern-card-title">
                            <i class="fas fa-info-circle"></i>
                            <span>Thông tin cơ bản</span>
                        </div>
                    </div>
                    <div class="modern-card-body">
                        <div class="form-group">
                            <label for="name" class="modern-label">
                                Tên sản phẩm <span class="required">*</span>
                            </label>
                            <input type="text" class="modern-input" id="name" name="name"
                                   value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>"
                                   placeholder="Nhập tên sản phẩm..." required>
                        </div>

                        <div class="form-group">
                            <label for="category_id" class="modern-label">
                                Danh mục <span class="required">*</span>
                            </label>
                            <select class="modern-select" id="category_id" name="category_id" required>
                                <option value="">-- Chọn danh mục --</option>
                                <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>" <?php echo (isset($_POST['category_id']) && $_POST['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="sku" class="modern-label">SKU (Mã sản phẩm)</label>
                            <input type="text" class="modern-input" id="sku" name="sku"
                                   value="<?php echo htmlspecialchars($_POST['sku'] ?? ''); ?>"
                                   placeholder="Ví dụ: NTBV-SP001">
                            <div class="modern-help-text">
                                <i class="fas fa-info-circle"></i>
                                Để trống để hệ thống tự động tạo SKU theo format: <strong>NTBV-SP001, NTBV-SP002, ...</strong>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="description" class="modern-label">Mô tả ngắn</label>
                            <textarea class="modern-textarea" id="description" name="description" rows="3"
                                      placeholder="Nhập mô tả ngắn về sản phẩm..."><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- Card mô tả chi tiết sản phẩm -->
                <div class="modern-card">
                    <div class="modern-card-header">
                        <div class="modern-card-title">
                            <i class="fas fa-file-alt"></i>
                            <span>Mô tả chi tiết sản phẩm</span>
                        </div>
                    </div>
                    <div class="modern-card-body">
                        <!-- Modern Tabs -->
                        <div class="modern-tabs">
                            <div class="modern-tab-nav">
                                <div class="modern-tab-btn active" data-tab="tab-overview">
                                    <i class="fas fa-info-circle"></i>
                                    <span>Tổng quan</span>
                                </div>
                                <div class="modern-tab-btn" data-tab="tab-features">
                                    <i class="fas fa-star"></i>
                                    <span>Đặc điểm nổi bật</span>
                                </div>
                                <div class="modern-tab-btn" data-tab="tab-specifications">
                                    <i class="fas fa-clipboard-list"></i>
                                    <span>Thông số kỹ thuật</span>
                                </div>
                                <div class="modern-tab-btn" data-tab="tab-usage-guide">
                                    <i class="fas fa-book"></i>
                                    <span>Hướng dẫn sử dụng</span>
                                </div>
                                <div class="modern-tab-btn" data-tab="tab-warranty">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>Bảo hành</span>
                                </div>
                            </div>

                            <!-- Tab Contents -->
                            <div class="modern-tab-content">
                                <!-- Tab Tổng quan -->
                                <div id="tab-overview" class="modern-tab-pane active">
                                    <div class="form-group">
                                        <label for="overview" class="modern-label">Tổng quan sản phẩm</label>
                                        <div class="modern-info-box">
                                            <div class="modern-info-header">
                                                <i class="fas fa-lightbulb"></i>
                                                <span>Hướng dẫn sử dụng trình soạn thảo</span>
                                            </div>
                                            <div class="modern-info-content">
                                                <ul>
                                                    <li>Sử dụng thanh công cụ phía trên để định dạng văn bản, chèn hình ảnh, v.v.</li>
                                                    <li>Để chèn hình ảnh, nhấn vào nút <i class="fas fa-image"></i> trên thanh công cụ.</li>
                                                    <li>Hình ảnh sẽ được tải lên và hiển thị với khung chú thích (caption) tùy chọn.</li>
                                                    <li>Bạn có thể thêm nhiều đoạn văn và hình ảnh theo ý muốn.</li>
                                                </ul>
                                            </div>
                                        </div>
                                        <textarea class="modern-textarea" id="product-overview-editor" name="overview" rows="5"
                                                  placeholder="Nhập tổng quan về sản phẩm..."><?php echo htmlspecialchars($_POST['overview'] ?? ''); ?></textarea>
                                        <div class="modern-help-text">
                                            <i class="fas fa-info-circle"></i>
                                            Mô tả tổng quan về sản phẩm, nguồn gốc, ý tưởng thiết kế và các đặc điểm chung.
                                        </div>
                                    </div>
                                </div>

                                <!-- Tab Đặc điểm nổi bật -->
                                <div id="tab-features" class="modern-tab-pane">
                                    <div class="form-group">
                                        <label class="modern-label">Đặc điểm nổi bật</label>
                                        <div id="features-container">
                                            <?php
                                            $features = isset($_POST['features']) ? $_POST['features'] : ['', '', '', ''];
                                            foreach ($features as $feature):
                                            ?>
                                            <div class="feature-row mb-2 flex items-center">
                                                <input type="text" name="features[]" class="modern-input flex-grow mr-2" placeholder="Nhập đặc điểm nổi bật" value="<?php echo htmlspecialchars($feature); ?>">
                                                <button type="button" class="modern-btn-danger remove-feature-btn">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <button type="button" id="add-feature-btn" class="modern-btn-success">
                                            <i class="fas fa-plus"></i>
                                            <span>Thêm đặc điểm</span>
                                        </button>
                                    </div>
                                </div>

                                <!-- Tab Thông số kỹ thuật -->
                                <div id="tab-specifications" class="modern-tab-pane">
                                    <div class="form-group">
                                        <label class="modern-label">Thông số kỹ thuật</label>
                                        <div id="specifications-container">
                                            <div class="size-options-header">
                                                <div class="size-option-label">Tên thông số</div>
                                                <div class="size-price-label">Giá trị</div>
                                                <div class="size-action-label">Thao tác</div>
                                            </div>

                                            <?php
                                            // Mặc định hiển thị các thông số cơ bản
                                            $default_specs = [
                                                'Chất liệu' => $material ?? '',
                                                'Kích thước' => $dimensions ?? '',
                                                'Màu sắc' => $color ?? '',
                                                'Xuất xứ' => 'Việt Nam',
                                                'Bảo hành' => '12 tháng'
                                            ];

                                            // Nếu có dữ liệu POST, sử dụng dữ liệu đó
                                            $spec_keys = isset($_POST['spec_keys']) ? $_POST['spec_keys'] : array_keys($default_specs);
                                            $spec_values = isset($_POST['spec_values']) ? $_POST['spec_values'] : array_values($default_specs);

                                            for ($i = 0; $i < count($spec_keys); $i++):
                                                $key = isset($spec_keys[$i]) ? $spec_keys[$i] : '';
                                                $value = isset($spec_values[$i]) ? $spec_values[$i] : '';
                                            ?>
                                            <div class="specification-row size-option-row">
                                                <div class="size-option-input">
                                                    <input type="text" name="spec_keys[]" class="modern-input" placeholder="Tên thông số" value="<?php echo htmlspecialchars($key); ?>">
                                                </div>
                                                <div class="size-price-input">
                                                    <input type="text" name="spec_values[]" class="modern-input" placeholder="Giá trị" value="<?php echo htmlspecialchars($value); ?>">
                                                </div>
                                                <div class="size-action">
                                                    <button type="button" class="modern-btn-danger remove-spec-btn">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <?php endfor; ?>
                                        </div>
                                        <button type="button" id="add-specification-btn" class="modern-btn-success">
                                            <i class="fas fa-plus"></i>
                                            <span>Thêm thông số</span>
                                        </button>
                                    </div>
                                </div>

                                <!-- Tab Hướng dẫn sử dụng -->
                                <div id="tab-usage-guide" class="modern-tab-pane">
                                    <div class="form-group">
                                        <label class="modern-label">Các bước sử dụng</label>
                                        <div id="usage-steps-container">
                                            <?php
                                            $default_steps = [
                                                'Vệ sinh thường xuyên bằng khăn mềm, khô',
                                                'Tránh đặt sản phẩm dưới ánh nắng trực tiếp',
                                                'Tránh va đập mạnh vào sản phẩm',
                                                'Định kỳ kiểm tra và siết chặt các ốc vít, bản lề (nếu có)'
                                            ];

                                            $steps = isset($_POST['usage_steps']) ? $_POST['usage_steps'] : $default_steps;
                                            foreach ($steps as $step):
                                            ?>
                                            <div class="step-row mb-2 flex items-center">
                                                <input type="text" name="usage_steps[]" class="modern-input flex-grow mr-2" placeholder="Nhập bước hướng dẫn sử dụng" value="<?php echo htmlspecialchars($step); ?>">
                                                <button type="button" class="modern-btn-danger remove-step-btn">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <button type="button" id="add-step-btn" class="modern-btn-success">
                                            <i class="fas fa-plus"></i>
                                            <span>Thêm bước</span>
                                        </button>
                                    </div>

                                    <div class="form-group mt-4">
                                        <label class="modern-label">Lưu ý khi sử dụng</label>
                                        <div id="usage-notes-container">
                                            <?php
                                            $default_notes = [
                                                'Không sử dụng các chất tẩy rửa có tính axit hoặc kiềm mạnh',
                                                'Không kéo lê sản phẩm trên sàn để tránh hư hỏng chân đế',
                                                'Đặt sản phẩm ở nơi khô ráo, thoáng mát, tránh nơi ẩm ướt'
                                            ];

                                            $notes = isset($_POST['usage_notes']) ? $_POST['usage_notes'] : $default_notes;
                                            foreach ($notes as $note):
                                            ?>
                                            <div class="note-row mb-2 flex items-center">
                                                <input type="text" name="usage_notes[]" class="modern-input flex-grow mr-2" placeholder="Nhập lưu ý khi sử dụng" value="<?php echo htmlspecialchars($note); ?>">
                                                <button type="button" class="modern-btn-danger remove-note-btn">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <button type="button" id="add-note-btn" class="modern-btn-success">
                                            <i class="fas fa-plus"></i>
                                            <span>Thêm lưu ý</span>
                                        </button>
                                    </div>
                                </div>

                                <!-- Tab Bảo hành -->
                                <div id="tab-warranty" class="modern-tab-pane">
                                    <div class="form-group">
                                        <label for="warranty_time" class="modern-label">Thời gian bảo hành</label>
                                        <input type="text" class="modern-input" id="warranty_time" name="warranty_time" placeholder="Ví dụ: 12 tháng" value="<?php echo htmlspecialchars($_POST['warranty_time'] ?? '12 tháng'); ?>">
                                    </div>

                                    <div class="form-group">
                                        <label for="warranty_scope" class="modern-label">Phạm vi bảo hành</label>
                                        <textarea class="modern-textarea" id="warranty_scope" name="warranty_scope" rows="2" placeholder="Mô tả phạm vi bảo hành..."><?php echo htmlspecialchars($_POST['warranty_scope'] ?? 'Bảo hành toàn bộ sản phẩm'); ?></textarea>
                                    </div>

                                    <div class="form-group">
                                        <label class="modern-label">Điều kiện bảo hành</label>
                                        <div id="warranty-conditions-container">
                                            <?php
                                            $default_conditions = [
                                                'Sản phẩm còn trong thời hạn bảo hành',
                                                'Sản phẩm bị lỗi do nhà sản xuất',
                                                'Sản phẩm được sử dụng đúng cách theo hướng dẫn'
                                            ];

                                            $conditions = isset($_POST['warranty_conditions']) ? $_POST['warranty_conditions'] : $default_conditions;
                                            foreach ($conditions as $condition):
                                            ?>
                                            <div class="condition-row mb-2 flex items-center">
                                                <input type="text" name="warranty_conditions[]" class="modern-input flex-grow mr-2" placeholder="Nhập điều kiện bảo hành" value="<?php echo htmlspecialchars($condition); ?>">
                                                <button type="button" class="modern-btn-danger remove-condition-btn">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <button type="button" id="add-condition-btn" class="modern-btn-success">
                                            <i class="fas fa-plus"></i>
                                            <span>Thêm điều kiện</span>
                                        </button>
                                    </div>

                                    <div class="form-group">
                                        <label for="warranty_contact" class="modern-label">Thông tin liên hệ bảo hành</label>
                                        <textarea class="modern-textarea" id="warranty_contact" name="warranty_contact" rows="2" placeholder="Thông tin liên hệ bảo hành..."><?php echo htmlspecialchars($_POST['warranty_contact'] ?? 'Hotline: 1900.1234 - Email: <EMAIL>'); ?></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- Giữ lại trường content cho tương thích ngược -->
                            <input type="hidden" id="content" name="content" value="<?php echo htmlspecialchars($_POST['content'] ?? ''); ?>">
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Card hình ảnh -->
                <div class="modern-card">
                    <div class="modern-card-header">
                        <div class="modern-card-title">
                            <i class="fas fa-images"></i>
                            <span>Hình ảnh sản phẩm</span>
                        </div>
                    </div>
                    <div class="modern-card-body">
                        <div class="form-group">
                            <label for="image" class="modern-label">
                                Hình ảnh chính <span class="required">*</span>
                            </label>

                            <!-- No Image Placeholder -->
                            <div id="no-image-placeholder" class="mb-3">
                                <div class="no-image-placeholder">
                                    <i class="fas fa-image"></i>
                                    <span>Chưa có hình ảnh</span>
                                </div>
                            </div>

                            <!-- New Image Preview -->
                            <div id="new-image-preview" class="mb-3" style="display: none;">
                                <div class="new-image-wrapper">
                                    <img id="new-main-image" class="new-image-preview" alt="Preview">
                                    <div class="image-overlay">
                                        <span class="image-label">Hình ảnh đã chọn</span>
                                        <button type="button" class="remove-preview-btn" id="remove-main-preview">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                    <div class="image-info" id="main-image-info"></div>
                                </div>
                            </div>

                            <!-- File Upload Area -->
                            <div class="modern-file-upload" id="main-file-upload">
                                <input type="file" class="modern-file-input" id="image" name="image" accept="image/*">
                                <div class="modern-file-upload-area">
                                    <div class="upload-content">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <span class="upload-text">Chọn hình ảnh chính</span>
                                        <div class="upload-loading" id="main-upload-loading" style="display: none;">
                                            <i class="fas fa-spinner fa-spin"></i>
                                            <span>Đang xử lý...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="modern-help-text">
                                <i class="fas fa-info-circle"></i>
                                Hỗ trợ các định dạng: JPG, JPEG, PNG, GIF, WebP. Kích thước tối đa: 5MB.
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="gallery" class="modern-label">Hình ảnh phụ</label>

                            <!-- New Gallery Images Preview -->
                            <div id="new-gallery-preview" class="mb-3" style="display: none;">
                                <p class="mb-2 font-weight-bold">Hình ảnh phụ đã chọn:</p>
                                <div class="new-gallery-grid" id="new-gallery-grid">
                                    <!-- New images will be inserted here -->
                                </div>
                            </div>

                            <!-- File Upload Area for Gallery -->
                            <div class="modern-file-upload" id="gallery-file-upload">
                                <input type="file" class="modern-file-input" id="gallery" name="gallery[]" multiple accept="image/*">
                                <div class="modern-file-upload-area">
                                    <div class="upload-content">
                                        <i class="fas fa-images"></i>
                                        <span class="upload-text">Chọn nhiều hình ảnh</span>
                                        <div class="upload-loading" id="gallery-upload-loading" style="display: none;">
                                            <i class="fas fa-spinner fa-spin"></i>
                                            <span>Đang xử lý...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Gallery Upload Status -->
                            <div id="gallery-upload-status" class="mt-2" style="display: none;">
                                <div class="upload-status-content">
                                    <i class="fas fa-check-circle text-success"></i>
                                    <span id="gallery-status-text"></span>
                                </div>
                            </div>

                            <div class="modern-help-text">
                                <i class="fas fa-info-circle"></i>
                                Chọn nhiều hình ảnh cùng lúc bằng cách giữ phím Ctrl (hoặc Command trên Mac) và click chọn. Kích thước tối đa mỗi file: 5MB.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Card giá cả và số lượng -->
                <div class="modern-card">
                    <div class="modern-card-header">
                        <div class="modern-card-title">
                            <i class="fas fa-dollar-sign"></i>
                            <span>Giá cả & Số lượng</span>
                        </div>
                    </div>
                    <div class="modern-card-body">
                        <div class="form-group">
                            <label for="price" class="modern-label">
                                Giá <span class="required">*</span>
                            </label>
                            <div class="modern-input-group">
                                <input type="number" class="modern-input" id="price" name="price" min="0" step="1000"
                                       value="<?php echo htmlspecialchars($_POST['price'] ?? '0'); ?>"
                                       placeholder="0" required>
                                <span class="modern-input-suffix">VNĐ</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="sale_price" class="modern-label">Giá khuyến mãi</label>
                            <div class="modern-input-group">
                                <input type="number" class="modern-input" id="sale_price" name="sale_price" min="0" step="1000"
                                       value="<?php echo htmlspecialchars($_POST['sale_price'] ?? ''); ?>"
                                       placeholder="0">
                                <span class="modern-input-suffix">VNĐ</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="quantity" class="modern-label">
                                Số lượng <span class="required">*</span>
                            </label>
                            <input type="number" class="modern-input" id="quantity" name="quantity" min="0"
                                   value="<?php echo htmlspecialchars($_POST['quantity'] ?? '0'); ?>"
                                   placeholder="0" required>
                        </div>

                        <div class="form-group">
                            <label for="price_type" class="modern-label">Loại giá</label>
                            <select class="modern-select" id="price_type" name="price_type">
                                <option value="fixed" <?php echo (isset($_POST['price_type']) && $_POST['price_type'] == 'fixed') ? 'selected' : ''; ?>>Giá cố định</option>
                                <option value="contact" <?php echo (isset($_POST['price_type']) && $_POST['price_type'] == 'contact') ? 'selected' : ''; ?>>Liên hệ báo giá</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Card thuộc tính sản phẩm -->
                <div class="modern-card">
                    <div class="modern-card-header">
                        <div class="modern-card-title">
                            <i class="fas fa-cogs"></i>
                            <span>Thuộc tính sản phẩm</span>
                        </div>
                    </div>
                    <div class="modern-card-body">
                        <div class="form-group">
                            <label for="material" class="modern-label">Chất liệu</label>
                            <input type="text" class="modern-input" id="material" name="material"
                                   value="<?php echo htmlspecialchars($_POST['material'] ?? ''); ?>"
                                   placeholder="Ví dụ: Gỗ sồi tự nhiên">
                        </div>

                        <div class="form-group">
                            <label for="dimensions" class="modern-label">Kích thước</label>
                            <input type="text" class="modern-input" id="dimensions" name="dimensions"
                                   value="<?php echo htmlspecialchars($_POST['dimensions'] ?? ''); ?>"
                                   placeholder="Ví dụ: 200 x 100 x 75 cm">
                        </div>

                        <div class="form-group">
                            <label for="color" class="modern-label">Màu sắc</label>
                            <input type="text" class="modern-input" id="color" name="color"
                                   value="<?php echo htmlspecialchars($_POST['color'] ?? ''); ?>"
                                   placeholder="Ví dụ: Nâu gỗ tự nhiên">
                        </div>
                    </div>
                </div>

                <!-- Card thống kê và đánh giá -->
                <div class="modern-card">
                    <div class="modern-card-header">
                        <div class="modern-card-title">
                            <i class="fas fa-chart-line"></i>
                            <span>Thống kê & Đánh giá</span>
                        </div>
                    </div>
                    <div class="modern-card-body">
                        <div class="form-group">
                            <label for="rating" class="modern-label">Số sao đánh giá</label>
                            <select class="modern-select" id="rating" name="rating">
                                <option value="5" <?php echo (isset($_POST['rating']) && $_POST['rating'] == 5) ? 'selected' : ''; ?>>⭐⭐⭐⭐⭐ 5 sao</option>
                                <option value="4" <?php echo (isset($_POST['rating']) && $_POST['rating'] == 4) ? 'selected' : ''; ?>>⭐⭐⭐⭐ 4 sao</option>
                                <option value="3" <?php echo (isset($_POST['rating']) && $_POST['rating'] == 3) ? 'selected' : ''; ?>>⭐⭐⭐ 3 sao</option>
                                <option value="2" <?php echo (isset($_POST['rating']) && $_POST['rating'] == 2) ? 'selected' : ''; ?>>⭐⭐ 2 sao</option>
                                <option value="1" <?php echo (isset($_POST['rating']) && $_POST['rating'] == 1) ? 'selected' : ''; ?>>⭐ 1 sao</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="sold" class="modern-label">Lượt bán</label>
                            <input type="number" class="modern-input" id="sold" name="sold" min="0"
                                   value="<?php echo htmlspecialchars($_POST['sold'] ?? '0'); ?>"
                                   placeholder="0">
                        </div>

                        <div class="form-group">
                            <label for="views" class="modern-label">Lượt xem</label>
                            <input type="number" class="modern-input" id="views" name="views" min="0"
                                   value="<?php echo htmlspecialchars($_POST['views'] ?? '0'); ?>"
                                   placeholder="0">
                        </div>
                    </div>
                </div>

                <!-- Card tùy chọn hiển thị -->
                <div class="modern-card">
                    <div class="modern-card-header">
                        <div class="modern-card-title">
                            <i class="fas fa-toggle-on"></i>
                            <span>Tùy chọn hiển thị</span>
                        </div>
                    </div>
                    <div class="modern-card-body">
                        <div class="modern-checkbox-group">
                            <div class="modern-checkbox">
                                <input type="checkbox" id="status" name="status" <?php echo (isset($_POST['status'])) ? 'checked' : ''; ?> value="1">
                                <label for="status">
                                    <span class="modern-checkbox-icon"></span>
                                    <span class="modern-checkbox-text">Hiển thị sản phẩm</span>
                                </label>
                            </div>

                            <div class="modern-checkbox">
                                <input type="checkbox" id="featured" name="featured" <?php echo (isset($_POST['featured'])) ? 'checked' : ''; ?> value="1">
                                <label for="featured">
                                    <span class="modern-checkbox-icon"></span>
                                    <span class="modern-checkbox-text">Sản phẩm nổi bật</span>
                                </label>
                            </div>

                            <div class="modern-checkbox">
                                <input type="checkbox" id="flash_sale" name="flash_sale" <?php echo (isset($_POST['flash_sale'])) ? 'checked' : ''; ?> value="1">
                                <label for="flash_sale">
                                    <span class="modern-checkbox-icon"></span>
                                    <span class="modern-checkbox-text">Flash Sale</span>
                                </label>
                            </div>

                            <div class="modern-checkbox">
                                <input type="checkbox" id="show_on_homepage" name="show_on_homepage" <?php echo (isset($_POST['show_on_homepage'])) ? 'checked' : ''; ?> value="1">
                                <label for="show_on_homepage">
                                    <span class="modern-checkbox-icon"></span>
                                    <span class="modern-checkbox-text">Hiển thị trên trang chủ</span>
                                </label>
                                <div class="modern-help-text">
                                    <i class="fas fa-info-circle"></i>
                                    Sản phẩm sẽ được hiển thị trong phần "Sản phẩm nội thất bằng vũ" trên trang chủ.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Card SEO -->
                <div class="modern-card">
                    <div class="modern-card-header">
                        <div class="modern-card-title">
                            <i class="fas fa-search"></i>
                            <span>Tối ưu SEO</span>
                        </div>
                    </div>
                    <div class="modern-card-body">
                        <div class="form-group">
                            <label for="meta_title" class="modern-label">Meta Title</label>
                            <input type="text" class="modern-input" id="meta_title" name="meta_title"
                                   value="<?php echo htmlspecialchars($_POST['meta_title'] ?? ''); ?>"
                                   placeholder="Tiêu đề SEO cho trang sản phẩm">
                        </div>

                        <div class="form-group">
                            <label for="meta_description" class="modern-label">Meta Description</label>
                            <textarea class="modern-textarea" id="meta_description" name="meta_description" rows="3"
                                      placeholder="Mô tả SEO cho trang sản phẩm..."><?php echo htmlspecialchars($_POST['meta_description'] ?? ''); ?></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card tùy chọn kích thước và giá - Full width -->
        <div class="modern-card">
            <div class="modern-card-header">
                <div class="modern-card-title">
                    <i class="fas fa-ruler-combined"></i>
                    <span>Tùy chọn kích thước và giá</span>
                </div>
            </div>
            <div class="modern-card-body">
                <div class="modern-help-text mb-3">
                    <i class="fas fa-info-circle"></i>
                    Thêm các tùy chọn kích thước và giá tương ứng cho sản phẩm (nếu có)
                </div>

                <div id="size-options-container">
                    <div class="size-options-header">
                        <div class="size-option-label">Kích thước</div>
                        <div class="size-price-label">Giá (VNĐ)</div>
                        <div class="size-action-label">Thao tác</div>
                    </div>

                    <div class="size-option-row">
                        <div class="size-option-input">
                            <input type="text" class="modern-input" name="size_option[]" placeholder="Ví dụ: 1m8 x 2m">
                        </div>
                        <div class="size-price-input">
                            <input type="number" class="modern-input" name="size_price[]" min="0" step="1000" placeholder="0">
                        </div>
                        <div class="size-action">
                            <button type="button" class="modern-btn-danger remove-size-option">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <button type="button" id="add-size-option" class="modern-btn-success">
                    <i class="fas fa-plus"></i>
                    <span>Thêm kích thước</span>
                </button>
            </div>
        </div>

        <!-- Action buttons -->
        <div class="modern-action-buttons">
            <button type="submit" class="btn-modern-primary">
                <i class="fas fa-plus"></i>
                <span>Thêm sản phẩm</span>
            </button>
            <button type="button" id="fill-sample-data" class="btn-modern-info">
                <i class="fas fa-magic"></i>
                <span>Nhập dữ liệu mẫu</span>
            </button>
            <a href="products.php" class="btn-modern-secondary">
                <i class="fas fa-times"></i>
                <span>Hủy</span>
            </a>
        </div>
    </form>
</div>

<style>
/* Modern Product Add Page Styles - Nội Thất Bàng Vũ Theme */
:root {
    /* Primary Colors - Cam chính từ brand */
    --primary: #F37321;
    --primary-dark: #D65A0F;
    --primary-darker: #D35400;
    --primary-light: #FF8A3D;
    --primary-lighter: #FFA66B;
    --primary-lightest: #FFD0AD;
    --primary-ultra-light: #FFF4EC;

    /* Secondary Colors - Xanh đậm */
    --secondary: #2A3B47;
    --secondary-dark: #1E2A32;
    --secondary-light: #435868;

    /* Status Colors */
    --success: #10B981;
    --warning: #F59E0B;
    --danger: #EF4444;
    --info: #3B82F6;

    /* Gradients với màu brand */
    --primary-gradient: linear-gradient(135deg, #F37321 0%, #D65A0F 100%);
    --secondary-gradient: linear-gradient(135deg, #2A3B47 0%, #1E2A32 100%);
    --success-gradient: linear-gradient(135deg, #10B981 0%, #059669 100%);
    --warning-gradient: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
    --danger-gradient: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
    --info-gradient: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);

    /* Neutral Colors */
    --gray-50: #F9FAFB;
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-400: #9CA3AF;
    --gray-500: #6B7280;
    --gray-600: #4B5563;
    --gray-700: #374151;
    --gray-800: #1F2937;
    --gray-900: #111827;

    /* Shadows */
    --card-shadow: 0 10px 30px rgba(243, 115, 33, 0.08);
    --card-shadow-hover: 0 20px 40px rgba(243, 115, 33, 0.12);
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);

    /* Border Radius */
    --border-radius: 1.25rem;
    --border-radius-sm: 0.5rem;
    --border-radius-md: 0.75rem;
    --border-radius-lg: 1rem;

    /* Transitions */
    --transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    --transition-fast: all 0.2s ease;
}

/* Header Styles */
.product-add-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
    border-radius: var(--border-radius);
    padding: 1.5rem 2rem;
    margin-bottom: 2rem;
    border: 1px solid rgba(243, 115, 33, 0.1);
    position: relative;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
}

.product-add-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 20%, rgba(243, 115, 33, 0.08) 0%, transparent 40%),
        radial-gradient(circle at 80% 80%, rgba(42, 59, 71, 0.05) 0%, transparent 40%);
    pointer-events: none;
    opacity: 0.8;
}

.product-add-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.product-add-title-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.product-add-icon {
    font-size: 1.5rem;
    color: white;
    background: var(--primary-gradient);
    padding: 1rem;
    border-radius: var(--border-radius);
    width: 3.5rem;
    height: 3.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    box-shadow: 0 6px 10px -2px rgba(243, 115, 33, 0.25);
}

.product-add-title-text h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0 0 0.5rem 0;
    line-height: 1.2;
}

.product-add-subtitle {
    color: var(--gray-600);
    font-size: 0.875rem;
    margin: 0;
    font-weight: 400;
}

.product-add-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Modern Alert Styles */
.modern-alert {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.25rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    border: 1px solid;
    position: relative;
    overflow: hidden;
}

.modern-alert-danger {
    background: linear-gradient(135deg, #FEF2F2 0%, #FECACA 100%);
    border-color: #FCA5A5;
    color: var(--danger);
}

.modern-alert-icon {
    font-size: 1.25rem;
    margin-top: 0.125rem;
}

.modern-alert-content h4 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
}

.modern-alert-content ul {
    margin: 0;
    padding-left: 1.25rem;
}

/* Modern Card Styles */
.modern-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    margin-bottom: 2rem;
    border: 1px solid var(--gray-200);
    transition: var(--transition);
    overflow: hidden;
}

.modern-card:hover {
    box-shadow: var(--card-shadow-hover);
    transform: translateY(-2px);
}

.modern-card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    background: linear-gradient(to right, var(--gray-50), white);
}

.modern-card-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.modern-card-title i {
    color: var(--primary);
    font-size: 1.25rem;
    background: var(--primary-ultra-light);
    padding: 0.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(243, 115, 33, 0.1);
}

.modern-card-body {
    padding: 1.5rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.modern-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.required {
    color: var(--danger);
    font-weight: 700;
}

.modern-input,
.modern-select,
.modern-textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius-md);
    font-size: 0.875rem;
    transition: var(--transition-fast);
    background: white;
    color: var(--gray-700);
}

.modern-input:focus,
.modern-select:focus,
.modern-textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.1);
    background: var(--primary-ultra-light);
}

.modern-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.modern-input-suffix {
    position: absolute;
    right: 1rem;
    color: var(--gray-500);
    font-size: 0.875rem;
    font-weight: 500;
    pointer-events: none;
}

.modern-help-text {
    font-size: 0.75rem;
    color: var(--gray-500);
    margin-top: 0.5rem;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    line-height: 1.4;
}

.modern-help-text i {
    color: var(--info);
    margin-top: 0.125rem;
    flex-shrink: 0;
}

/* File Upload Styles */
.modern-file-upload {
    position: relative;
}

.modern-file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.modern-file-upload-area {
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius-md);
    padding: 2rem;
    text-align: center;
    transition: var(--transition-fast);
    background: var(--gray-50);
    cursor: pointer;
}

.modern-file-upload-area:hover {
    border-color: var(--primary);
    background: var(--primary-ultra-light);
}

.modern-file-upload-area i {
    font-size: 2rem;
    color: var(--primary);
    margin-bottom: 0.5rem;
    display: block;
}

.modern-file-upload-area span {
    color: var(--gray-600);
    font-weight: 500;
}

/* Info Box Styles */
.modern-info-box {
    background: linear-gradient(135deg, var(--info-gradient));
    border-radius: var(--border-radius-md);
    margin-bottom: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.modern-info-header {
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: white;
    font-weight: 600;
}

.modern-info-content {
    padding: 1rem;
    background: rgba(255, 255, 255, 0.95);
    color: var(--gray-700);
}

.modern-info-content ul {
    margin: 0;
    padding-left: 1.25rem;
}

.modern-info-content li {
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

/* Tab Styles */
.modern-tabs {
    margin-bottom: 1.5rem;
}

.modern-tab-nav {
    display: flex;
    border-bottom: 2px solid var(--gray-200);
    margin-bottom: 1.5rem;
    overflow-x: auto;
    gap: 0.5rem;
}

.modern-tab-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    border: none;
    background: none;
    color: var(--gray-600);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
    white-space: nowrap;
    position: relative;
}

.modern-tab-btn:hover {
    color: var(--primary);
    background: var(--primary-ultra-light);
}

.modern-tab-btn.active {
    color: var(--primary);
    background: var(--primary-ultra-light);
    border-bottom: 2px solid var(--primary);
}

.modern-tab-btn i {
    font-size: 1rem;
}

.modern-tab-content {
    min-height: 200px;
}

.modern-tab-pane {
    display: none;
}

.modern-tab-pane.active {
    display: block;
}

/* Checkbox Styles */
.modern-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.modern-checkbox {
    position: relative;
}

.modern-checkbox input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.modern-checkbox label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: var(--border-radius-md);
    transition: var(--transition-fast);
    border: 1px solid var(--gray-200);
    background: white;
}

.modern-checkbox label:hover {
    background: var(--primary-ultra-light);
    border-color: var(--primary-light);
}

.modern-checkbox-icon {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
    flex-shrink: 0;
}

.modern-checkbox input[type="checkbox"]:checked + label .modern-checkbox-icon {
    background: var(--primary-gradient);
    border-color: var(--primary);
}

.modern-checkbox input[type="checkbox"]:checked + label .modern-checkbox-icon::after {
    content: '✓';
    color: white;
    font-weight: bold;
    font-size: 0.75rem;
}

.modern-checkbox-text {
    font-weight: 500;
    color: var(--gray-700);
}

/* Button Styles */
.btn-modern-primary,
.btn-modern-secondary,
.btn-modern-info,
.modern-btn-success,
.modern-btn-danger {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    font-size: 0.875rem;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn-modern-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(243, 115, 33, 0.2);
}

.btn-modern-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-darker) 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(243, 115, 33, 0.3);
    text-decoration: none;
}

.btn-modern-secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-modern-secondary:hover {
    background: var(--gray-200);
    color: var(--gray-800);
    transform: translateY(-2px);
    text-decoration: none;
}

.btn-modern-info {
    background: var(--info-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.btn-modern-info:hover {
    background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.modern-btn-success {
    background: var(--success-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

.modern-btn-success:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

.modern-btn-danger {
    background: var(--danger-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
    padding: 0.5rem;
    width: 36px;
    height: 36px;
    justify-content: center;
}

.modern-btn-danger:hover {
    background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);
}

/* Size Options Styles */
.size-options-header {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: var(--gray-50);
    border-radius: var(--border-radius-md);
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.size-option-row {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 1rem;
    margin-bottom: 1rem;
    align-items: center;
}

/* Action Buttons - Sticky Bottom Full Width (excluding sidebar) */
.modern-action-buttons {
    position: fixed;
    bottom: 0;
    left: 280px;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 1rem;
    padding: 1rem 2rem;
    background: white;
    border-top: 1px solid var(--gray-200);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: all 0.3s ease;
}

/* Add bottom padding to prevent content being hidden behind sticky buttons */
.container-fluid {
    padding-bottom: 100px;
}

/* Feature, Step, Note, Condition Rows */
.feature-row,
.step-row,
.note-row,
.condition-row {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.flex-grow {
    flex: 1;
}

.mr-2 {
    margin-right: 0.5rem;
}

.mt-4 {
    margin-top: 1.5rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

/* Responsive */
@media (max-width: 768px) {
    .product-add-header-content {
        flex-direction: column;
        align-items: stretch;
    }

    .modern-tab-nav {
        flex-wrap: wrap;
    }

    .modern-action-buttons {
        left: 0;
        flex-direction: column;
        gap: 0.75rem;
        padding: 1rem;
    }

    .modern-action-buttons .btn-modern-primary,
    .modern-action-buttons .btn-modern-info,
    .modern-action-buttons .btn-modern-secondary {
        width: 100%;
        justify-content: center;
    }

    .size-options-header,
    .size-option-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .feature-row,
    .step-row,
    .note-row,
    .condition-row {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .mr-2 {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
}

/* Image Preview Styles */
.current-image-wrapper,
.new-image-wrapper {
    position: relative;
    display: inline-block;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.current-image-wrapper:hover,
.new-image-wrapper:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.current-image-preview,
.new-image-preview {
    width: 200px;
    height: 150px;
    object-fit: cover;
    display: block;
    border-radius: var(--border-radius-md);
}

.no-image-placeholder {
    width: 200px;
    height: 150px;
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius-md);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--gray-500);
    background: var(--gray-50);
}

.no-image-placeholder i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
    color: white;
    padding: 0.75rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.image-label {
    font-size: 0.75rem;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}

.remove-preview-btn {
    background: var(--danger);
    border: none;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.75rem;
}

.remove-preview-btn:hover {
    background: #DC2626;
    transform: scale(1.1);
}

.image-info {
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: var(--gray-50);
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    color: var(--gray-600);
}

/* Gallery Preview Styles */
.current-gallery-grid,
.new-gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.current-gallery-item,
.new-gallery-item {
    position: relative;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.current-gallery-item:hover,
.new-gallery-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.gallery-image-wrapper {
    position: relative;
    width: 100%;
    height: 100px;
}

.gallery-image-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* Upload Area Enhancements */
.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.upload-loading {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary);
    font-weight: 500;
}

.upload-loading i {
    font-size: 1rem;
}

.modern-file-upload-area.dragover {
    border-color: var(--primary);
    background: var(--primary-ultra-light);
    transform: scale(1.02);
}

.modern-file-upload-area.processing {
    pointer-events: none;
    opacity: 0.7;
}

/* Upload Status */
.upload-status-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: linear-gradient(135deg, #F0FDF4 0%, #DCFCE7 100%);
    border: 1px solid #BBF7D0;
    border-radius: var(--border-radius-md);
    color: var(--success);
    font-size: 0.875rem;
    font-weight: 500;
}

.upload-status-content i {
    font-size: 1rem;
}

/* New Gallery Item Specific Styles */
.new-gallery-item {
    position: relative;
}

.new-gallery-item .remove-preview-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    z-index: 10;
}

.new-gallery-item .image-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 0.25rem 0.5rem;
    font-size: 0.625rem;
    margin: 0;
    border-radius: 0;
}

/* Error States */
.upload-error {
    border-color: var(--danger) !important;
    background: #FEF2F2 !important;
}

.error-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: linear-gradient(135deg, #FEF2F2 0%, #FECACA 100%);
    border: 1px solid #FCA5A5;
    border-radius: var(--border-radius-md);
    color: var(--danger);
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 0.5rem;
}

.error-message i {
    font-size: 1rem;
}

/* Responsive Design for Image Previews */
@media (max-width: 768px) {
    .current-image-preview,
    .new-image-preview,
    .no-image-placeholder {
        width: 100%;
        max-width: 300px;
        height: 200px;
    }

    .current-gallery-grid,
    .new-gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 0.75rem;
    }

    .gallery-image-wrapper {
        height: 80px;
    }
}

@media (max-width: 480px) {
    .current-gallery-grid,
    .new-gallery-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Custom height for Summernote editor on desktop */
@media (min-width: 768px) {
    .note-editable {
        min-height: 1000px !important;
        height: 1000px !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab functionality
    const tabBtns = document.querySelectorAll('.modern-tab-btn');
    const tabPanes = document.querySelectorAll('.modern-tab-pane');

    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Remove active class from all tabs and panes
            tabBtns.forEach(b => b.classList.remove('active'));
            tabPanes.forEach(p => p.classList.remove('active'));

            // Add active class to clicked tab and corresponding pane
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });

    // Image Preview Functionality
    initializeImagePreview();

    // Xử lý thêm tùy chọn kích thước
    const addSizeOptionBtn = document.getElementById('add-size-option');
    const sizeOptionsContainer = document.getElementById('size-options-container');

    addSizeOptionBtn.addEventListener('click', function() {
        const newRow = document.createElement('div');
        newRow.className = 'size-option-row';
        newRow.innerHTML = `
            <div class="size-option-input">
                <input type="text" class="modern-input" name="size_option[]" placeholder="Ví dụ: 1m8 x 2m">
            </div>
            <div class="size-price-input">
                <input type="number" class="modern-input" name="size_price[]" min="0" step="1000" placeholder="0">
            </div>
            <div class="size-action">
                <button type="button" class="modern-btn-danger remove-size-option">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        sizeOptionsContainer.appendChild(newRow);

        // Thêm sự kiện xóa cho nút mới
        const removeBtn = newRow.querySelector('.remove-size-option');
        removeBtn.addEventListener('click', function() {
            sizeOptionsContainer.removeChild(newRow);
        });
    });

    // Xử lý xóa tùy chọn kích thước
    document.querySelectorAll('.remove-size-option').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const row = this.closest('.size-option-row');
            sizeOptionsContainer.removeChild(row);
        });
    });



    // Xử lý nút nhập dữ liệu mẫu
    const fillSampleDataBtn = document.getElementById('fill-sample-data');
    fillSampleDataBtn.addEventListener('click', function() {
        // Dữ liệu mẫu cho các trường
        const sampleData = {
            name: 'Sofa da cao cấp Milano',
            category_id: '1', // Chọn danh mục đầu tiên, bạn có thể thay đổi nếu cần
            description: 'Sofa da cao cấp Milano được nhập khẩu trực tiếp từ Ý, với chất liệu da bò thật 100%, khung gỗ sồi tự nhiên và đệm mút cao cấp mang lại cảm giác êm ái, thoải mái cho người sử dụng.',
            price: '15000000',
            sale_price: '12000000',
            quantity: '10',
            material: 'Da bò thật 100%, khung gỗ sồi tự nhiên',
            dimensions: '2400 x 900 x 750 mm (Dài x Rộng x Cao)',
            color: 'Nâu đậm',
            rating: '5',
            sold: '8',
            views: '120',
            meta_title: 'Sofa da cao cấp Milano - Nội thất Bàng Vũ',
            meta_description: 'Sofa da cao cấp Milano được nhập khẩu trực tiếp từ Ý, với chất liệu da bò thật 100%, khung gỗ sồi tự nhiên. Mang đến không gian sống sang trọng và đẳng cấp.'
        };

        // Điền dữ liệu vào các trường
        document.getElementById('name').value = sampleData.name;

        // Chọn danh mục
        const categorySelect = document.getElementById('category_id');
        if (categorySelect.options.length > 1) {
            categorySelect.value = sampleData.category_id;
        }

        document.getElementById('description').value = sampleData.description;
        document.getElementById('price').value = sampleData.price;
        document.getElementById('sale_price').value = sampleData.sale_price;
        document.getElementById('quantity').value = sampleData.quantity;
        document.getElementById('material').value = sampleData.material;
        document.getElementById('dimensions').value = sampleData.dimensions;
        document.getElementById('color').value = sampleData.color;
        document.getElementById('rating').value = sampleData.rating;
        document.getElementById('sold').value = sampleData.sold;
        document.getElementById('views').value = sampleData.views;
        document.getElementById('meta_title').value = sampleData.meta_title;
        document.getElementById('meta_description').value = sampleData.meta_description;

        // Chọn các checkbox
        document.getElementById('status').checked = true;
        document.getElementById('featured').checked = true;
        document.getElementById('flash_sale').checked = true;

        // Thêm các tùy chọn kích thước và giá
        // Xóa tất cả các tùy chọn hiện tại trừ hàng đầu tiên
        const sizeOptionRows = document.querySelectorAll('.size-option-row');
        for (let i = 1; i < sizeOptionRows.length; i++) {
            sizeOptionsContainer.removeChild(sizeOptionRows[i]);
        }

        // Điền dữ liệu cho hàng đầu tiên
        const firstRow = sizeOptionRows[0];
        if (firstRow) {
            const sizeInput = firstRow.querySelector('input[name="size_option[]"]');
            const priceInput = firstRow.querySelector('input[name="size_price[]"]');
            if (sizeInput && priceInput) {
                sizeInput.value = '2400 x 900 x 750 mm';
                priceInput.value = '15000000';
            }
        }

        // Thêm các tùy chọn kích thước và giá khác
        const additionalSizes = [
            { size: '2800 x 900 x 750 mm', price: '18000000' },
            { size: '3200 x 900 x 750 mm', price: '22000000' }
        ];

        additionalSizes.forEach(item => {
            // Tạo hàng mới
            const newRow = document.createElement('div');
            newRow.className = 'size-option-row';
            newRow.innerHTML = `
                <div class="size-option-input">
                    <input type="text" class="modern-input" name="size_option[]" value="${item.size}" placeholder="Ví dụ: 1m8 x 2m">
                </div>
                <div class="size-price-input">
                    <input type="number" class="modern-input" name="size_price[]" value="${item.price}" min="0" step="1000" placeholder="0">
                </div>
                <div class="size-action">
                    <button type="button" class="modern-btn-danger remove-size-option">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            sizeOptionsContainer.appendChild(newRow);

            // Thêm sự kiện xóa cho nút mới
            const removeBtn = newRow.querySelector('.remove-size-option');
            removeBtn.addEventListener('click', function() {
                sizeOptionsContainer.removeChild(newRow);
            });
        });

        // Thông báo cho người dùng
        alert('Đã điền dữ liệu mẫu vào form. Vui lòng thêm hình ảnh sản phẩm trước khi lưu.');
    });

    // Image Preview Functions
    function initializeImagePreview() {
        // Main image preview
        const mainImageInput = document.getElementById('image');
        const galleryInput = document.getElementById('gallery');

        if (mainImageInput) {
            mainImageInput.addEventListener('change', handleMainImageChange);
            setupDragAndDrop(document.getElementById('main-file-upload'), mainImageInput);
        }

        if (galleryInput) {
            galleryInput.addEventListener('change', handleGalleryChange);
            setupDragAndDrop(document.getElementById('gallery-file-upload'), galleryInput);
        }

        // Remove preview buttons
        const removeMainPreview = document.getElementById('remove-main-preview');
        if (removeMainPreview) {
            removeMainPreview.addEventListener('click', function() {
                clearMainImagePreview();
            });
        }
    }

    function handleMainImageChange(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Show loading
        showMainImageLoading(true);

        // Validate file
        const validation = validateImageFile(file);
        if (!validation.valid) {
            showMainImageError(validation.message);
            showMainImageLoading(false);
            return;
        }

        // Create preview
        const reader = new FileReader();
        reader.onload = function(e) {
            showMainImagePreview(e.target.result, file);
            showMainImageLoading(false);
        };
        reader.onerror = function() {
            showMainImageError('Không thể đọc file hình ảnh');
            showMainImageLoading(false);
        };
        reader.readAsDataURL(file);
    }

    function handleGalleryChange(event) {
        const files = Array.from(event.target.files);
        if (files.length === 0) return;

        // Show loading
        showGalleryLoading(true);

        // Validate all files
        const validFiles = [];
        const errors = [];

        files.forEach((file, index) => {
            const validation = validateImageFile(file);
            if (validation.valid) {
                validFiles.push(file);
            } else {
                errors.push(`File ${file.name}: ${validation.message}`);
            }
        });

        if (errors.length > 0) {
            showGalleryError(errors.join('<br>'));
        }

        if (validFiles.length > 0) {
            createGalleryPreviews(validFiles);
            showGalleryStatus(`Đã chọn ${validFiles.length} hình ảnh`);
        }

        showGalleryLoading(false);
    }

    // Helper Functions
    function validateImageFile(file) {
        const maxSize = 5 * 1024 * 1024; // 5MB
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];

        if (!allowedTypes.includes(file.type)) {
            return {
                valid: false,
                message: 'Định dạng file không được hỗ trợ. Chỉ chấp nhận: JPG, JPEG, PNG, GIF, WebP'
            };
        }

        if (file.size > maxSize) {
            return {
                valid: false,
                message: 'Kích thước file quá lớn. Tối đa 5MB'
            };
        }

        return { valid: true };
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function showMainImagePreview(src, file) {
        const noImagePlaceholder = document.getElementById('no-image-placeholder');
        const newImagePreview = document.getElementById('new-image-preview');
        const newMainImage = document.getElementById('new-main-image');
        const mainImageInfo = document.getElementById('main-image-info');

        // Hide placeholder
        noImagePlaceholder.style.display = 'none';

        // Show preview
        newMainImage.src = src;
        mainImageInfo.innerHTML = `
            <strong>${file.name}</strong><br>
            Kích thước: ${formatFileSize(file.size)}<br>
            Định dạng: ${file.type.split('/')[1].toUpperCase()}
        `;

        newImagePreview.style.display = 'block';
        clearMainImageError();
    }

    function clearMainImagePreview() {
        const noImagePlaceholder = document.getElementById('no-image-placeholder');
        const newImagePreview = document.getElementById('new-image-preview');
        const mainImageInput = document.getElementById('image');

        // Show placeholder
        noImagePlaceholder.style.display = 'block';

        // Hide preview
        newImagePreview.style.display = 'none';
        mainImageInput.value = '';
        clearMainImageError();
    }

    function showMainImageLoading(show) {
        const uploadText = document.querySelector('#main-file-upload .upload-text');
        const uploadLoading = document.getElementById('main-upload-loading');
        const uploadArea = document.querySelector('#main-file-upload .modern-file-upload-area');

        if (show) {
            uploadText.style.display = 'none';
            uploadLoading.style.display = 'flex';
            uploadArea.classList.add('processing');
        } else {
            uploadText.style.display = 'block';
            uploadLoading.style.display = 'none';
            uploadArea.classList.remove('processing');
        }
    }

    function showMainImageError(message) {
        clearMainImageError();
        const uploadArea = document.getElementById('main-file-upload');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.id = 'main-image-error';
        errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i><span>${message}</span>`;
        uploadArea.appendChild(errorDiv);

        // Clear input
        document.getElementById('image').value = '';
    }

    function clearMainImageError() {
        const existingError = document.getElementById('main-image-error');
        if (existingError) {
            existingError.remove();
        }
    }

    function createGalleryPreviews(files) {
        const newGalleryPreview = document.getElementById('new-gallery-preview');
        const newGalleryGrid = document.getElementById('new-gallery-grid');

        // Clear existing previews
        newGalleryGrid.innerHTML = '';

        files.forEach((file, index) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const galleryItem = document.createElement('div');
                galleryItem.className = 'new-gallery-item';
                galleryItem.innerHTML = `
                    <div class="gallery-image-wrapper">
                        <img src="${e.target.result}" class="gallery-image-preview" alt="Preview ${index + 1}">
                        <button type="button" class="remove-preview-btn" onclick="removeGalleryPreview(this)">
                            <i class="fas fa-times"></i>
                        </button>
                        <div class="image-info">
                            ${file.name} (${formatFileSize(file.size)})
                        </div>
                    </div>
                `;
                newGalleryGrid.appendChild(galleryItem);
            };
            reader.readAsDataURL(file);
        });

        newGalleryPreview.style.display = 'block';
        clearGalleryError();
    }

    function showGalleryLoading(show) {
        const uploadText = document.querySelector('#gallery-file-upload .upload-text');
        const uploadLoading = document.getElementById('gallery-upload-loading');
        const uploadArea = document.querySelector('#gallery-file-upload .modern-file-upload-area');

        if (show) {
            uploadText.style.display = 'none';
            uploadLoading.style.display = 'flex';
            uploadArea.classList.add('processing');
        } else {
            uploadText.style.display = 'block';
            uploadLoading.style.display = 'none';
            uploadArea.classList.remove('processing');
        }
    }

    function showGalleryStatus(message) {
        const statusDiv = document.getElementById('gallery-upload-status');
        const statusText = document.getElementById('gallery-status-text');

        statusText.textContent = message;
        statusDiv.style.display = 'block';

        // Auto hide after 3 seconds
        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 3000);
    }

    function showGalleryError(message) {
        clearGalleryError();
        const uploadArea = document.getElementById('gallery-file-upload');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.id = 'gallery-error';
        errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i><span>${message}</span>`;
        uploadArea.appendChild(errorDiv);
    }

    function clearGalleryError() {
        const existingError = document.getElementById('gallery-error');
        if (existingError) {
            existingError.remove();
        }
    }

    function setupDragAndDrop(uploadArea, input) {
        const dropArea = uploadArea.querySelector('.modern-file-upload-area');

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, unhighlight, false);
        });

        function highlight(e) {
            dropArea.classList.add('dragover');
        }

        function unhighlight(e) {
            dropArea.classList.remove('dragover');
        }

        dropArea.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            // Create a new FileList-like object
            const fileArray = Array.from(files);
            const dataTransfer = new DataTransfer();
            fileArray.forEach(file => dataTransfer.items.add(file));

            input.files = dataTransfer.files;
            input.dispatchEvent(new Event('change', { bubbles: true }));
        }
    }

    // Global function for removing gallery preview items
    window.removeGalleryPreview = function(button) {
        const galleryItem = button.closest('.new-gallery-item');
        const galleryGrid = document.getElementById('new-gallery-grid');
        const galleryPreview = document.getElementById('new-gallery-preview');

        galleryItem.remove();

        // Hide preview container if no items left
        if (galleryGrid.children.length === 0) {
            galleryPreview.style.display = 'none';
            // Clear the file input
            document.getElementById('gallery').value = '';
        }
    };

});
</script>

<style>
/* Simple Animation for container-fluid - Consistent with other admin pages */
.container-fluid {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<?php
// Thêm script quản lý sản phẩm
echo '<script src="' . BASE_URL . '/admin/assets/js/product-management.js"></script>';

// Include footer
include_once 'partials/footer.php';
?>
