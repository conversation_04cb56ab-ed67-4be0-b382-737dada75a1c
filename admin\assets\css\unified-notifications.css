/**
 * Unified Notifications CSS - <PERSON><PERSON><PERSON> Thất Bàng <PERSON>
 * CSS bổ sung cho hệ thống thông báo thống nhất
 */

/* <PERSON><PERSON><PERSON> bảo tất cả thông báo có transition mượt mà */
.alert,
.custom-alert,
.notification,
.flash-message,
[class*="alert-"],
[role="alert"] {
    transition: opacity 0.3s ease-out, transform 0.3s ease-out !important;
}

/* Hiệu ứng fade-out chung cho tất cả thông báo */
.alert.fade-out,
.custom-alert.fade-out,
.notification.fade-out,
.flash-message.fade-out,
[class*="alert-"].fade-out,
[role="alert"].fade-out {
    opacity: 0 !important;
    transform: translateY(-10px) !important;
    pointer-events: none;
}

/* Hiệu ứng fade-in cho thông báo mới */
.alert.fade-in,
.custom-alert.fade-in,
.notification.fade-in,
.flash-message.fade-in,
[class*="alert-"].fade-in,
[role="alert"].fade-in {
    animation: unifiedFadeIn 0.3s ease-out forwards;
}

@keyframes unifiedFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Container cho thông báo */
.notification-container {
    position: relative;
    z-index: 1050;
    margin-bottom: 1rem;
}

/* Thông báo thống nhất */
.unified-notification {
    position: relative;
    margin-bottom: 1rem;
    border-radius: 0.375rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Đảm bảo thông báo không bị che khuất */
.unified-notification,
.alert,
.custom-alert {
    z-index: 1040;
    position: relative;
}

/* Responsive cho thông báo */
@media (max-width: 768px) {
    .notification-container {
        margin-left: -15px;
        margin-right: -15px;
    }

    .unified-notification,
    .alert,
    .custom-alert {
        margin-left: 15px;
        margin-right: 15px;
        border-radius: 0;
    }
}

/* Đảm bảo thông báo có độ ưu tiên cao */
.alert[data-auto-hide-processed],
.custom-alert[data-auto-hide-processed] {
    position: relative;
    z-index: 1041;
}

/* Hiệu ứng hover cho nút đóng */
.alert .close:hover,
.custom-alert .alert-close:hover {
    opacity: 1;
    transform: scale(1.1);
    transition: all 0.2s ease;
}

/* Đảm bảo thông báo không bị overflow */
.container-fluid .notification-container {
    overflow: visible;
}

/* Style cho thông báo debug (nếu cần) */
.debug-notification {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #6c757d;
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

/* Đảm bảo animation không bị conflict */
.alert,
.custom-alert {
    animation-fill-mode: both;
}

/* Override cho các thông báo có animation khác */
.alert.show {
    animation: none;
    opacity: 1;
    transform: none;
}

/* Đảm bảo thông báo luôn hiển thị trên cùng */
.alert-container {
    position: relative;
    z-index: 1050;
}

/* Style cho progress bar nếu có */
.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 0 0 0.375rem 0.375rem;
    overflow: hidden;
}

.notification-progress-bar {
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    width: 100%;
    transform: translateX(-100%);
    transition: transform linear;
}

/* Đảm bảo text trong thông báo không bị wrap không mong muốn */
.alert,
.custom-alert {
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Style cho icon trong thông báo */
.alert i,
.custom-alert i {
    flex-shrink: 0;
    margin-right: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

/* Đảm bảo icon trong alert-icon được căn chỉnh hoàn hảo */
.alert-icon,
.notification-icon {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center;
    line-height: 1 !important;
}

.alert-icon i,
.notification-icon i {
    display: block;
    width: auto;
    height: auto;
    line-height: 1;
    text-align: center;
    vertical-align: middle;
}

/* Khắc phục vấn đề căn chỉnh cho FontAwesome icons */
.fas,
.far,
.fab {
    vertical-align: middle;
    line-height: 1;
}

/* Đảm bảo pseudo-elements (::before) cũng được căn chỉnh tốt */
.alert::before {
    text-align: center !important;
    vertical-align: middle !important;
    line-height: 1 !important;
}

/* Đảm bảo thông báo có khoảng cách phù hợp */
.alert + .alert,
.custom-alert + .custom-alert,
.alert + .custom-alert,
.custom-alert + .alert {
    margin-top: 0.5rem;
}
