<?php
// Include các file cần thiết
require_once '../includes/init.php';

// Thiết lập tiêu đề trang
$page_title = 'Cập nhật cơ sở dữ liệu';

// Ki<PERSON>m tra quyền truy cập
if (!is_admin()) {
    set_flash_message('error', 'Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/admin/index.php');
}

// Khởi tạo biến
$success_messages = [];
$error_messages = [];

// Xử lý cập nhật từ file SQL nếu có
if (isset($_GET['file']) && !empty($_GET['file'])) {
    $sql_file = '../sql/' . basename($_GET['file']);

    if (file_exists($sql_file)) {
        // Đọc nội dung file SQL
        $sql_content = file_get_contents($sql_file);

        // Tách các câu lệnh SQL
        $sql_queries = explode(';', $sql_content);

        // Thực thi từng câu lệnh SQL
        $executed = 0;
        $failed = 0;

        foreach ($sql_queries as $query) {
            $query = trim($query);

            if (!empty($query)) {
                try {
                    if ($conn->query($query)) {
                        $executed++;
                    } else {
                        $failed++;
                        $error_messages[] = 'Lỗi khi thực thi câu lệnh: ' . $query;
                    }
                } catch (Exception $e) {
                    $failed++;
                    $error_messages[] = 'Lỗi: ' . $e->getMessage() . ' trong câu lệnh: ' . $query;
                }
            }
        }

        if ($executed > 0) {
            $success_messages[] = "Đã thực thi thành công $executed câu lệnh SQL từ file " . basename($sql_file);
        }

        if ($failed > 0) {
            $error_messages[] = "Có $failed câu lệnh SQL thất bại.";
        }
    } else {
        $error_messages[] = 'Không tìm thấy file SQL: ' . basename($sql_file);
    }
}

// Kiểm tra thư mục uploads/blog/content
$blog_content_upload_dir = UPLOADS_PATH . 'blog/content/';
if (!file_exists($blog_content_upload_dir)) {
    if (mkdir($blog_content_upload_dir, 0755, true)) {
        $success_messages[] = 'Đã tạo thư mục uploads/blog/content thành công.';
    } else {
        $error_messages[] = 'Không thể tạo thư mục uploads/blog/content. Vui lòng tạo thủ công.';
    }
}

// Kiểm tra thư mục uploads/products/content
$product_content_upload_dir = UPLOADS_PATH . 'products/content/';
if (!file_exists($product_content_upload_dir)) {
    if (mkdir($product_content_upload_dir, 0755, true)) {
        $success_messages[] = 'Đã tạo thư mục uploads/products/content thành công.';
    } else {
        $error_messages[] = 'Không thể tạo thư mục uploads/products/content. Vui lòng tạo thủ công.';
    }
}

// Include file header
include_once 'partials/header.php';
?>

<!-- Phần HTML của trang -->
<div class="container-fluid">
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Cập nhật cơ sở dữ liệu</h6>
            <a href="<?php echo BASE_URL; ?>/admin/index.php" class="btn btn-sm btn-secondary">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>
        <div class="card-body">
            <?php if (!empty($error_messages)): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php foreach ($error_messages as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if (!empty($success_messages)): ?>
                <div class="alert alert-success">
                    <ul class="mb-0">
                        <?php foreach ($success_messages as $success): ?>
                            <li><?php echo $success; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if (isset($_GET['file']) && !empty($_GET['file'])): ?>
            <div class="alert alert-info">
                <p>Hệ thống đang thực hiện cập nhật cơ sở dữ liệu từ file: <strong><?php echo htmlspecialchars(basename($_GET['file'])); ?></strong></p>
                <p>Vui lòng đợi trong giây lát...</p>
            </div>
            <?php else: ?>
            <div class="alert alert-info">
                <p>Hệ thống đã được cập nhật để hỗ trợ trình soạn thảo Summernote cho nội dung blog và chi tiết sản phẩm.</p>
                <p>Các thay đổi đã được thực hiện:</p>
                <ul>
                    <li>Thêm trình soạn thảo Summernote vào trang thêm/sửa bài viết blog</li>
                    <li>Thêm trình soạn thảo Summernote vào phần tổng quan sản phẩm</li>
                    <li>Hỗ trợ tải lên hình ảnh trực tiếp từ trình soạn thảo</li>
                    <li>Tạo thư mục uploads/blog/content để lưu trữ hình ảnh blog</li>
                    <li>Tạo thư mục uploads/products/content để lưu trữ hình ảnh sản phẩm</li>
                </ul>
                <p>Bạn có thể bắt đầu sử dụng trình soạn thảo ngay bây giờ.</p>
            </div>
            <?php endif; ?>

            <div class="text-center mt-4">
                <?php if (isset($_GET['file']) && !empty($_GET['file'])): ?>
                <a href="<?php echo BASE_URL; ?>/admin/index.php" class="btn btn-primary">
                    <i class="fas fa-home"></i> Quay về trang chủ
                </a>
                <?php if (strpos($_GET['file'], 'testimonials') !== false): ?>
                <a href="<?php echo BASE_URL; ?>/admin/testimonials.php" class="btn btn-success ml-2">
                    <i class="fas fa-quote-left"></i> Quản lý cảm nhận khách hàng
                </a>
                <?php endif; ?>
                <?php else: ?>
                <a href="<?php echo BASE_URL; ?>/admin/blog-posts.php" class="btn btn-primary">
                    <i class="fas fa-newspaper"></i> Quản lý bài viết
                </a>
                <a href="<?php echo BASE_URL; ?>/admin/blog-post-edit.php" class="btn btn-success ml-2">
                    <i class="fas fa-plus"></i> Thêm bài viết mới
                </a>
                <a href="<?php echo BASE_URL; ?>/admin/products.php" class="btn btn-primary ml-2">
                    <i class="fas fa-box"></i> Quản lý sản phẩm
                </a>
                <a href="<?php echo BASE_URL; ?>/admin/product-add.php" class="btn btn-success ml-2">
                    <i class="fas fa-plus"></i> Thêm sản phẩm mới
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
// Include file footer
include_once 'partials/footer.php';
?>
