/**
 * CustomEditor - Trình soạn thảo văn bản đơn giản cho website Nội thất Bàng Vũ
 *
 * Tính năng:
 * - Định dạng văn bản cơ bản (đậm, nghiêng, gạch chân, mà<PERSON> chữ)
 * - Cỡ chữ và kiểu chữ (font size, font family)
 * - Chèn hình ảnh từ máy tính với caption
 * - Chèn liên kết
 * - Ch<PERSON><PERSON> danh sách có thứ tự và không có thứ tự
 * - Chèn bảng
 * - Chèn tiêu đề (h1, h2, h3, h4, h5, h6)
 * - Xem mã HTML
 */

class CustomEditor {
  /**
   * Khởi tạo trình soạn thảo
   * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn cho trình soạn thảo
   */
  constructor(options) {
    this.options = Object.assign(
      {
        element: null,
        height: '300px',
        placeholder: 'Nhập nội dung...',
        uploadUrl: '',
        value: '',
        onChange: null,
      },
      options
    );

    if (!this.options.element) {
      throw new Error('Không tìm thấy phần tử để khởi tạo trình soạn thảo');
    }

    this.init();
  }

  /**
   * Khởi tạo trình soạn thảo
   */
  init() {
    // Tạo container cho trình soạn thảo
    this.container = document.createElement('div');
    this.container.className = 'custom-editor';
    this.container.style.border = '1px solid #ddd';
    this.container.style.borderRadius = '4px';
    this.container.style.overflow = 'hidden';

    // Tạo thanh công cụ
    this.toolbar = document.createElement('div');
    this.toolbar.className = 'custom-editor-toolbar';
    this.toolbar.style.padding = '10px';
    this.toolbar.style.backgroundColor = '#f8f9fa';
    this.toolbar.style.borderBottom = '1px solid #ddd';
    this.container.appendChild(this.toolbar);

    // Tạo các nút trên thanh công cụ
    this.createToolbarButtons();

    // Tạo khu vực soạn thảo
    this.editorArea = document.createElement('div');
    this.editorArea.className = 'custom-editor-area';
    this.editorArea.style.padding = '15px';
    this.editorArea.style.minHeight = this.options.height;
    this.editorArea.style.maxHeight = '600px';
    this.editorArea.style.overflowY = 'auto';
    this.editorArea.contentEditable = true;
    this.editorArea.style.outline = 'none';
    this.editorArea.style.fontSize = '14px';
    this.editorArea.style.lineHeight = '1.6';
    this.editorArea.innerHTML = this.options.value || '';
    this.editorArea.setAttribute('placeholder', this.options.placeholder);
    this.container.appendChild(this.editorArea);

    // Thêm CSS cho placeholder
    const style = document.createElement('style');
    style.textContent = `
            .custom-editor-area:empty:before {
                content: attr(placeholder);
                color: #aaa;
                font-style: italic;
            }
        `;
    document.head.appendChild(style);

    // Thêm trình soạn thảo vào DOM
    this.options.element.parentNode.insertBefore(
      this.container,
      this.options.element.nextSibling
    );
    this.options.element.style.display = 'none';

    // Thêm sự kiện cho khu vực soạn thảo
    this.addEditorEvents();

    // Cập nhật giá trị ban đầu
    this.updateValue();
  }

  /**
   * Tạo các nút trên thanh công cụ
   */
  createToolbarButtons() {
    // Tạo các nhóm nút
    const formatGroup = this.createButtonGroup();
    const fontGroup = this.createButtonGroup();
    const headingGroup = this.createButtonGroup();
    const alignGroup = this.createButtonGroup();
    const listGroup = this.createButtonGroup();
    const insertGroup = this.createButtonGroup();
    const viewGroup = this.createButtonGroup();

    // Thêm các nút định dạng văn bản
    formatGroup.appendChild(
      this.createButton('Đậm', '<i class="fas fa-bold"></i>', () =>
        this.execCommand('bold')
      )
    );
    formatGroup.appendChild(
      this.createButton('Nghiêng', '<i class="fas fa-italic"></i>', () =>
        this.execCommand('italic')
      )
    );
    formatGroup.appendChild(
      this.createButton('Gạch chân', '<i class="fas fa-underline"></i>', () =>
        this.execCommand('underline')
      )
    );
    formatGroup.appendChild(
      this.createButton(
        'Gạch ngang',
        '<i class="fas fa-strikethrough"></i>',
        () => this.execCommand('strikeThrough')
      )
    );
    formatGroup.appendChild(
      this.createColorPicker(
        'Màu chữ',
        '<i class="fas fa-palette"></i>',
        (color) => this.execCommand('foreColor', color)
      )
    );

    // Thêm bộ chọn cỡ chữ và kiểu chữ
    fontGroup.appendChild(
      this.createSelect(
        'Kiểu chữ',
        [
          { value: 'Arial, sans-serif', text: 'Arial' },
          { value: 'Helvetica, sans-serif', text: 'Helvetica' },
          { value: 'Times New Roman, serif', text: 'Times New Roman' },
          { value: 'Georgia, serif', text: 'Georgia' },
          { value: 'Courier New, monospace', text: 'Courier New' },
          { value: 'Verdana, sans-serif', text: 'Verdana' },
          { value: 'Tahoma, sans-serif', text: 'Tahoma' },
          { value: 'Trebuchet MS, sans-serif', text: 'Trebuchet MS' },
          { value: 'Impact, sans-serif', text: 'Impact' },
          { value: 'Comic Sans MS, cursive', text: 'Comic Sans MS' },
        ],
        (value) => this.execCommand('fontName', value)
      )
    );

    fontGroup.appendChild(
      this.createSelect(
        'Cỡ chữ',
        [
          { value: '1', text: 'Rất nhỏ' },
          { value: '2', text: 'Nhỏ' },
          { value: '3', text: 'Bình thường' },
          { value: '4', text: 'Lớn' },
          { value: '5', text: 'Lớn hơn' },
          { value: '6', text: 'Rất lớn' },
          { value: '7', text: 'Cực lớn' },
        ],
        (value) => this.execCommand('fontSize', value)
      )
    );

    // Thêm các nút tiêu đề
    headingGroup.appendChild(
      this.createSelect(
        'Tiêu đề',
        [
          { value: 'p', text: 'Đoạn văn' },
          { value: 'h1', text: 'Tiêu đề 1' },
          { value: 'h2', text: 'Tiêu đề 2' },
          { value: 'h3', text: 'Tiêu đề 3' },
          { value: 'h4', text: 'Tiêu đề 4' },
          { value: 'h5', text: 'Tiêu đề 5' },
          { value: 'h6', text: 'Tiêu đề 6' },
        ],
        (value) => this.formatBlock(value)
      )
    );

    // Thêm các nút căn lề
    alignGroup.appendChild(
      this.createButton('Căn trái', '<i class="fas fa-align-left"></i>', () =>
        this.execCommand('justifyLeft')
      )
    );
    alignGroup.appendChild(
      this.createButton('Căn giữa', '<i class="fas fa-align-center"></i>', () =>
        this.execCommand('justifyCenter')
      )
    );
    alignGroup.appendChild(
      this.createButton('Căn phải', '<i class="fas fa-align-right"></i>', () =>
        this.execCommand('justifyRight')
      )
    );
    alignGroup.appendChild(
      this.createButton('Căn đều', '<i class="fas fa-align-justify"></i>', () =>
        this.execCommand('justifyFull')
      )
    );

    // Thêm các nút danh sách
    listGroup.appendChild(
      this.createButton(
        'Danh sách có thứ tự',
        '<i class="fas fa-list-ol"></i>',
        () => this.execCommand('insertOrderedList')
      )
    );
    listGroup.appendChild(
      this.createButton(
        'Danh sách không có thứ tự',
        '<i class="fas fa-list-ul"></i>',
        () => this.execCommand('insertUnorderedList')
      )
    );
    listGroup.appendChild(
      this.createButton('Thụt lề', '<i class="fas fa-indent"></i>', () =>
        this.execCommand('indent')
      )
    );
    listGroup.appendChild(
      this.createButton('Lùi lề', '<i class="fas fa-outdent"></i>', () =>
        this.execCommand('outdent')
      )
    );

    // Thêm các nút chèn
    insertGroup.appendChild(
      this.createButton('Chèn liên kết', '<i class="fas fa-link"></i>', () =>
        this.insertLink()
      )
    );
    insertGroup.appendChild(
      this.createButton('Chèn hình ảnh', '<i class="fas fa-image"></i>', () =>
        this.insertImage()
      )
    );
    insertGroup.appendChild(
      this.createButton('Chèn bảng', '<i class="fas fa-table"></i>', () =>
        this.insertTable()
      )
    );

    // Thêm các nút xem
    viewGroup.appendChild(
      this.createButton('Xem mã HTML', '<i class="fas fa-code"></i>', () =>
        this.toggleHtmlView()
      )
    );

    // Thêm các nhóm nút vào thanh công cụ
    this.toolbar.appendChild(formatGroup);
    this.toolbar.appendChild(this.createSeparator());
    this.toolbar.appendChild(fontGroup);
    this.toolbar.appendChild(this.createSeparator());
    this.toolbar.appendChild(headingGroup);
    this.toolbar.appendChild(this.createSeparator());
    this.toolbar.appendChild(alignGroup);
    this.toolbar.appendChild(this.createSeparator());
    this.toolbar.appendChild(listGroup);
    this.toolbar.appendChild(this.createSeparator());
    this.toolbar.appendChild(insertGroup);
    this.toolbar.appendChild(this.createSeparator());
    this.toolbar.appendChild(viewGroup);
  }

  /**
   * Tạo nhóm nút
   * @returns {HTMLElement} Nhóm nút
   */
  createButtonGroup() {
    const group = document.createElement('div');
    group.className = 'custom-editor-button-group';
    group.style.display = 'inline-block';
    group.style.marginRight = '10px';
    return group;
  }

  /**
   * Tạo nút
   * @param {string} title - Tiêu đề nút
   * @param {string} icon - HTML của biểu tượng
   * @param {Function} onClick - Hàm xử lý khi nhấn nút
   * @returns {HTMLElement} Nút
   */
  createButton(title, icon, onClick) {
    const button = document.createElement('button');
    button.type = 'button';
    button.className = 'custom-editor-button';
    button.title = title;
    button.innerHTML = icon;
    button.style.border = 'none';
    button.style.background = 'none';
    button.style.cursor = 'pointer';
    button.style.padding = '5px';
    button.style.borderRadius = '3px';
    button.style.marginRight = '2px';
    button.addEventListener('mouseover', () => {
      button.style.backgroundColor = '#e9ecef';
    });
    button.addEventListener('mouseout', () => {
      button.style.backgroundColor = 'transparent';
    });
    button.addEventListener('click', onClick);
    return button;
  }

  /**
   * Tạo bộ chọn màu
   * @param {string} title - Tiêu đề bộ chọn màu
   * @param {string} icon - HTML của biểu tượng
   * @param {Function} onSelect - Hàm xử lý khi chọn màu
   * @returns {HTMLElement} Bộ chọn màu
   */
  createColorPicker(title, icon, onSelect) {
    const container = document.createElement('div');
    container.className = 'custom-editor-color-picker';
    container.style.display = 'inline-block';
    container.style.position = 'relative';

    const button = this.createButton(title, icon, () => {
      colorPicker.style.display =
        colorPicker.style.display === 'none' ? 'block' : 'none';
    });
    container.appendChild(button);

    const colorPicker = document.createElement('div');
    colorPicker.className = 'custom-editor-color-picker-panel';
    colorPicker.style.display = 'none';
    colorPicker.style.position = 'absolute';
    colorPicker.style.top = '30px';
    colorPicker.style.left = '0';
    colorPicker.style.zIndex = '1000';
    colorPicker.style.backgroundColor = '#fff';
    colorPicker.style.border = '1px solid #ddd';
    colorPicker.style.borderRadius = '4px';
    colorPicker.style.padding = '5px';
    colorPicker.style.width = '150px';
    colorPicker.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
    container.appendChild(colorPicker);

    const colors = [
      '#000000',
      '#434343',
      '#666666',
      '#999999',
      '#b7b7b7',
      '#cccccc',
      '#d9d9d9',
      '#efefef',
      '#f3f3f3',
      '#ffffff',
      '#980000',
      '#ff0000',
      '#ff9900',
      '#ffff00',
      '#00ff00',
      '#00ffff',
      '#4a86e8',
      '#0000ff',
      '#9900ff',
      '#ff00ff',
      '#e6b8af',
      '#f4cccc',
      '#fce5cd',
      '#fff2cc',
      '#d9ead3',
      '#d0e0e3',
      '#c9daf8',
      '#cfe2f3',
      '#d9d2e9',
      '#ead1dc',
    ];

    const colorGrid = document.createElement('div');
    colorGrid.style.display = 'grid';
    colorGrid.style.gridTemplateColumns = 'repeat(10, 1fr)';
    colorGrid.style.gridGap = '2px';

    colors.forEach((color) => {
      const colorCell = document.createElement('div');
      colorCell.style.backgroundColor = color;
      colorCell.style.width = '12px';
      colorCell.style.height = '12px';
      colorCell.style.cursor = 'pointer';
      colorCell.style.border = '1px solid #ddd';
      colorCell.addEventListener('click', () => {
        onSelect(color);
        colorPicker.style.display = 'none';
      });
      colorGrid.appendChild(colorCell);
    });

    colorPicker.appendChild(colorGrid);

    // Đóng bảng màu khi click ra ngoài
    document.addEventListener('click', (e) => {
      if (!container.contains(e.target)) {
        colorPicker.style.display = 'none';
      }
    });

    return container;
  }

  /**
   * Tạo bộ chọn
   * @param {string} title - Tiêu đề bộ chọn
   * @param {Array} options - Các tùy chọn
   * @param {Function} onSelect - Hàm xử lý khi chọn
   * @returns {HTMLElement} Bộ chọn
   */
  createSelect(title, options, onSelect) {
    const select = document.createElement('select');
    select.className = 'custom-editor-select';
    select.title = title;
    select.style.border = '1px solid #ddd';
    select.style.borderRadius = '3px';
    select.style.padding = '4px';
    select.style.backgroundColor = '#fff';
    select.style.cursor = 'pointer';

    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.text = title;
    defaultOption.disabled = true;
    defaultOption.selected = true;
    select.appendChild(defaultOption);

    options.forEach((option) => {
      const optionElement = document.createElement('option');
      optionElement.value = option.value;
      optionElement.text = option.text;
      select.appendChild(optionElement);
    });

    select.addEventListener('change', () => {
      onSelect(select.value);
      select.selectedIndex = 0; // Reset về giá trị mặc định
    });

    return select;
  }

  /**
   * Tạo dấu phân cách
   * @returns {HTMLElement} Dấu phân cách
   */
  createSeparator() {
    const separator = document.createElement('div');
    separator.className = 'custom-editor-separator';
    separator.style.display = 'inline-block';
    separator.style.width = '1px';
    separator.style.height = '20px';
    separator.style.backgroundColor = '#ddd';
    separator.style.margin = '0 5px';
    separator.style.verticalAlign = 'middle';
    return separator;
  }

  /**
   * Thêm sự kiện cho khu vực soạn thảo
   */
  addEditorEvents() {
    // Sự kiện khi nội dung thay đổi
    this.editorArea.addEventListener('input', () => {
      this.updateValue();
    });

    // Sự kiện khi paste
    this.editorArea.addEventListener('paste', (e) => {
      e.preventDefault();
      const text = (e.originalEvent || e).clipboardData.getData('text/plain');
      document.execCommand('insertText', false, text);
    });

    // Sự kiện khi drop
    this.editorArea.addEventListener('drop', (e) => {
      const files = e.dataTransfer.files;
      if (files.length > 0 && files[0].type.match(/^image\//)) {
        e.preventDefault();
        this.uploadImage(files[0]);
      }
    });
  }

  /**
   * Thực thi lệnh
   * @param {string} command - Lệnh cần thực thi
   * @param {string} value - Giá trị của lệnh (nếu có)
   */
  execCommand(command, value = null) {
    document.execCommand(command, false, value);
    this.editorArea.focus();
    this.updateValue();
  }

  /**
   * Định dạng khối
   * @param {string} tag - Thẻ HTML cần áp dụng
   */
  formatBlock(tag) {
    if (tag === 'p') {
      document.execCommand('formatBlock', false, 'p');
    } else {
      document.execCommand('formatBlock', false, `<${tag}>`);
    }
    this.editorArea.focus();
    this.updateValue();
  }

  /**
   * Chèn liên kết
   */
  insertLink() {
    const url = prompt('Nhập URL:', 'http://');
    if (url) {
      document.execCommand('createLink', false, url);
      this.updateValue();
    }
  }

  /**
   * Chèn hình ảnh
   */
  insertImage() {
    if (!this.options.uploadUrl) {
      alert('Chưa cấu hình URL tải lên hình ảnh');
      return;
    }

    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.style.display = 'none';
    document.body.appendChild(input);

    input.addEventListener('change', () => {
      if (input.files.length > 0) {
        this.uploadImage(input.files[0]);
      }
      document.body.removeChild(input);
    });

    input.click();
  }

  /**
   * Tải lên hình ảnh
   * @param {File} file - File hình ảnh cần tải lên
   */
  uploadImage(file) {
    // Hiển thị thông báo đang tải lên
    const loadingMessage = document.createElement('div');
    loadingMessage.className = 'custom-editor-loading';
    loadingMessage.innerHTML =
      '<i class="fas fa-spinner fa-spin"></i> Đang tải lên hình ảnh...';
    loadingMessage.style.position = 'absolute';
    loadingMessage.style.top = '50%';
    loadingMessage.style.left = '50%';
    loadingMessage.style.transform = 'translate(-50%, -50%)';
    loadingMessage.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    loadingMessage.style.color = 'white';
    loadingMessage.style.padding = '10px 20px';
    loadingMessage.style.borderRadius = '5px';
    loadingMessage.style.zIndex = '9999';
    document.body.appendChild(loadingMessage);

    const formData = new FormData();
    formData.append('upload', file);

    const xhr = new XMLHttpRequest();
    xhr.open('POST', this.options.uploadUrl, true);
    xhr.onload = () => {
      // Xóa thông báo đang tải lên
      document.body.removeChild(loadingMessage);

      if (xhr.status === 200) {
        try {
          const response = JSON.parse(xhr.responseText);
          if (response.url) {
            // Hiển thị hộp thoại nhập caption
            const caption = prompt(
              'Nhập chú thích cho hình ảnh (để trống nếu không cần):',
              ''
            );

            // Tạo HTML cho hình ảnh với caption
            let imgHtml;
            if (caption) {
              imgHtml = `
                <figure style="margin: 1em 0; text-align: center;">
                  <img src="${response.url}" alt="${file.name}" style="max-width: 100%; height: auto; display: block; margin: 0 auto;">
                  <figcaption style="margin-top: 0.5em; font-style: italic; color: #666; font-size: 0.9em;">${caption}</figcaption>
                </figure>
              `;
            } else {
              imgHtml = `<img src="${response.url}" alt="${file.name}" style="max-width: 100%; height: auto; display: block; margin: 1em auto;">`;
            }

            // Chèn HTML vào trình soạn thảo
            document.execCommand('insertHTML', false, imgHtml);
            this.updateValue();
          } else if (response.error) {
            alert('Lỗi: ' + response.error);
          } else {
            alert('Lỗi không xác định khi tải lên hình ảnh');
          }
        } catch (e) {
          console.error('Lỗi khi xử lý phản hồi:', e);
          console.log('Phản hồi từ máy chủ:', xhr.responseText);
          alert(
            'Lỗi khi xử lý phản hồi từ máy chủ. Vui lòng kiểm tra console để biết thêm chi tiết.'
          );
        }
      } else {
        alert('Lỗi khi tải lên hình ảnh: ' + xhr.status + ' ' + xhr.statusText);
      }
    };
    xhr.onerror = () => {
      // Xóa thông báo đang tải lên
      document.body.removeChild(loadingMessage);
      alert('Lỗi kết nối khi tải lên hình ảnh');
    };
    xhr.send(formData);
  }

  /**
   * Chèn bảng
   */
  insertTable() {
    const rows = prompt('Số hàng:', '3');
    const cols = prompt('Số cột:', '3');

    if (rows && cols) {
      let table = '<table style="width:100%; border-collapse:collapse;">';
      for (let i = 0; i < parseInt(rows); i++) {
        table += '<tr>';
        for (let j = 0; j < parseInt(cols); j++) {
          table +=
            '<td style="border:1px solid #ddd; padding:8px;">&nbsp;</td>';
        }
        table += '</tr>';
      }
      table += '</table><p></p>';
      document.execCommand('insertHTML', false, table);
      this.updateValue();
    }
  }

  /**
   * Chuyển đổi giữa chế độ xem HTML và chế độ soạn thảo
   */
  toggleHtmlView() {
    if (this.editorArea.getAttribute('contenteditable') === 'true') {
      // Lưu trữ nội dung hiện tại
      this.editorContent = this.editorArea.innerHTML;

      // Chuyển sang chế độ xem HTML
      const textarea = document.createElement('textarea');
      textarea.className = 'custom-editor-html-view';
      textarea.style.width = '100%';
      textarea.style.height = this.options.height;
      textarea.style.padding = '15px';
      textarea.style.border = 'none';
      textarea.style.outline = 'none';
      textarea.style.fontFamily = 'monospace';
      textarea.style.fontSize = '14px';
      textarea.style.lineHeight = '1.6';
      textarea.value = this.editorContent;

      // Lưu trữ vị trí của editorArea
      const editorAreaParent = this.editorArea.parentNode;
      const editorAreaNextSibling = this.editorArea.nextSibling;

      // Xóa editorArea khỏi DOM
      editorAreaParent.removeChild(this.editorArea);

      // Thêm textarea vào DOM
      if (editorAreaNextSibling) {
        editorAreaParent.insertBefore(textarea, editorAreaNextSibling);
      } else {
        editorAreaParent.appendChild(textarea);
      }

      this.htmlTextarea = textarea;
      this.isHtmlView = true;
    } else {
      // Lưu trữ nội dung HTML
      const htmlContent = this.htmlTextarea.value;

      // Lưu trữ vị trí của htmlTextarea
      const textareaParent = this.htmlTextarea.parentNode;
      const textareaNextSibling = this.htmlTextarea.nextSibling;

      // Xóa htmlTextarea khỏi DOM
      textareaParent.removeChild(this.htmlTextarea);

      // Cập nhật nội dung cho editorArea
      this.editorArea.innerHTML = htmlContent;

      // Thêm editorArea vào DOM
      if (textareaNextSibling) {
        textareaParent.insertBefore(this.editorArea, textareaNextSibling);
      } else {
        textareaParent.appendChild(this.editorArea);
      }

      this.isHtmlView = false;
      this.updateValue();
    }
  }

  /**
   * Cập nhật giá trị
   */
  updateValue() {
    this.options.element.value = this.editorArea.innerHTML;
    if (typeof this.options.onChange === 'function') {
      this.options.onChange(this.options.element.value);
    }
  }

  /**
   * Lấy nội dung
   * @returns {string} Nội dung HTML
   */
  getContent() {
    return this.options.element.value;
  }

  /**
   * Đặt nội dung
   * @param {string} html - Nội dung HTML
   */
  setContent(html) {
    this.editorArea.innerHTML = html;
    this.updateValue();
  }
}
