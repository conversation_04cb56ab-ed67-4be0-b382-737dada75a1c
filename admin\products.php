<?php
// Include init
require_once '../includes/init.php';

// Ki<PERSON>m tra quyền admin
require_once 'partials/check_admin.php';

// Thiết lập tiêu đề trang
$page_title = 'Quản lý sản phẩm';

// L<PERSON>y thống kê nhanh cho header
$total_products_count = count_products();
$active_products_count = count_products(null, null, 1); // Sản phẩm đang hoạt động
$out_of_stock_count = count_products_out_of_stock(); // Cần tạo hàm này
$featured_products_count = count_featured_products(); // Cần tạo hàm này

// --- <PERSON><PERSON> lý các tham số GET cho bộ lọc và sắp xếp ---
$search = isset($_GET['search']) ? trim($_GET['search']) : null;
$category_id = isset($_GET['category_id']) && !empty($_GET['category_id']) ? intval($_GET['category_id']) : null;
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;

// Các tham số sắp xếp
$allowed_sort_columns_admin = ['name', 'price', 'quantity', 'created_at', 'sku']; // Các cột admin có thể sort
$current_sort_by = isset($_GET['sort_by']) && in_array($_GET['sort_by'], $allowed_sort_columns_admin) ? $_GET['sort_by'] : 'created_at';
$current_sort_order = isset($_GET['sort_order']) && in_array(strtoupper($_GET['sort_order']), ['ASC', 'DESC']) ? strtoupper($_GET['sort_order']) : 'DESC';

// Tham số lọc giá và số lượng (sẽ thêm input cho chúng sau)
$price_min_filter = isset($_GET['price_min']) && is_numeric($_GET['price_min']) ? (float)$_GET['price_min'] : null;
$price_max_filter = isset($_GET['price_max']) && is_numeric($_GET['price_max']) ? (float)$_GET['price_max'] : null;
$price_type_filter = isset($_GET['price_type_filter']) && in_array($_GET['price_type_filter'], ['fixed', 'contact']) ? $_GET['price_type_filter'] : null;

// Lấy các danh mục hiển thị trên trang chủ để tạo options cho dropdown
$homepage_display_categories = get_homepage_categories(); // Mặc định status=1

// Xử lý bộ lọc trang chủ mới
$homepage_filter_value = isset($_GET['homepage_filter']) ? $_GET['homepage_filter'] : ''; // Giá trị từ dropdown
$homepage_filter_mode_for_query = null; // Giá trị sẽ truyền vào get_products
$current_filter_category_id_for_query = $category_id; // Giữ lại category_id ban đầu nếu có
$current_featured_param_for_query = null; // featured_param sẽ được xử lý bởi homepage_filter_mode
$admin_enforced_status_filter = null; // Biến mới để quản lý status cho các bộ lọc trang chủ

if ($homepage_filter_value === 'all_hp') {
    $homepage_filter_mode_for_query = 'all_homepage';
    $current_filter_category_id_for_query = null; // Clear category filter
    $admin_enforced_status_filter = 1; // Lọc sản phẩm status = 1
} elseif ($homepage_filter_value === 'featured_hp') {
    $homepage_filter_mode_for_query = 'only_featured';
    $current_filter_category_id_for_query = null; // Clear category filter
    $admin_enforced_status_filter = 1; // Lọc sản phẩm status = 1
} elseif (strpos($homepage_filter_value, 'cat_hp_') === 0) {
    $hp_cat_id = (int)substr($homepage_filter_value, 7);
    if ($hp_cat_id > 0) {
        $homepage_filter_mode_for_query = 'cat_hp_' . $hp_cat_id;
        $admin_enforced_status_filter = 1; // Lọc sản phẩm status = 1
    }
} else {
    // Nếu homepage_filter rỗng hoặc không hợp lệ, sử dụng $category_id và $featured như bình thường
    // Tuy nhiên, chúng ta đã quyết định homepage_filter sẽ là bộ lọc chính cho hiển thị trang chủ.
    // Nếu không có homepage_filter, thì $featured_param (nếu có từ một nguồn khác) sẽ được dùng.
    // Hiện tại, chúng ta không có nguồn $featured nào khác vì đã xóa $filter_is_featured_get.
    // Vậy, nếu không có homepage_filter, sẽ không có bộ lọc đặc biệt nào về featured/homepage categories.
    $current_featured_param_for_query = isset($_GET['featured']) && in_array($_GET['featured'], ['0','1']) ? (int)$_GET['featured'] : null; // Fallback nếu có ai đó cố tình pass param `featured` trực tiếp
}

// Số sản phẩm trên mỗi trang
$allowed_items_per_page = [10, 20, 50, 100, 200, 'all'];
$current_items_per_page_value = isset($_GET['items_per_page']) ? $_GET['items_per_page'] : 20; // Mặc định 20 sản phẩm

// Debug the raw input
$debug_items_per_page = true; // Set to false in production
if ($debug_items_per_page) {
    error_log("Raw items_per_page from GET: " . var_export($_GET['items_per_page'] ?? 'not set', true));
    error_log("Current items_per_page_value before validation: " . var_export($current_items_per_page_value, true));
    error_log("Allowed values: " . var_export($allowed_items_per_page, true));
}

// Convert string numbers to integers for comparison
$allowed_items_per_page_for_comparison = [];
foreach ($allowed_items_per_page as $val) {
    $allowed_items_per_page_for_comparison[] = $val;
    if (is_numeric($val)) {
        $allowed_items_per_page_for_comparison[] = (string)$val; // Also allow string version
    }
}

// Check if the value is valid (allow both string and int versions)
$is_valid = in_array($current_items_per_page_value, $allowed_items_per_page_for_comparison) ||
            (is_numeric($current_items_per_page_value) && in_array((int)$current_items_per_page_value, $allowed_items_per_page));

if (!$is_valid) {
    if ($debug_items_per_page) {
        error_log("Invalid items_per_page value, falling back to default: " . $current_items_per_page_value);
    }
    $current_items_per_page_value = 20; // Fallback về giá trị mặc định nếu không hợp lệ
}

// Convert to integer if it's a numeric value
if (is_numeric($current_items_per_page_value)) {
    $current_items_per_page_value = (int)$current_items_per_page_value;
}

$limit = ($current_items_per_page_value === 'all') ? null : (int)$current_items_per_page_value;

if ($debug_items_per_page) {
    error_log("Final current_items_per_page_value: " . var_export($current_items_per_page_value, true));
    error_log("Final limit: " . var_export($limit, true));
}

// --- Kết thúc xử lý tham số GET ---

// Lưu query string hiện tại vào session để redirect sau bulk action
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
// Xây dựng lại query string không bao gồm CSRF token hoặc các action nhất thời
$query_params_for_session = [];
foreach ($_GET as $key => $value) {
    if (!in_array($key, ['csrf_token', 'action', 'id'])) { // Loại bỏ các tham số không cần lưu cho redirect
        $query_params_for_session[$key] = $value;
    }
}
$_SESSION['last_products_query'] = http_build_query($query_params_for_session);


// Phân trang
$offset = ($page - 1) * ($limit ?? 0);
if ($current_items_per_page_value === 'all') {
    $offset = 0; // Khi hiển thị tất cả, offset luôn là 0
}

// Chuẩn bị sort_options cho hàm get_products
$sort_options_for_query = [
    'by' => $current_sort_by,
    'order' => $current_sort_order
];

// Debug items per page logic
$debug_items_per_page = true; // Set to false in production
if ($debug_items_per_page) {
    error_log("Items per page debug: current_items_per_page_value = " . $current_items_per_page_value);
    error_log("Items per page debug: limit = " . ($limit ?? 'null'));
    error_log("Items per page debug: offset = " . $offset);
    error_log("Items per page debug: page = " . $page);
}

// Lấy danh sách sản phẩm
$products = get_products($limit, $offset, $current_filter_category_id_for_query, $current_featured_param_for_query, $search, $admin_enforced_status_filter, $price_min_filter, $price_max_filter, null, null, $sort_options_for_query, $homepage_filter_mode_for_query, $price_type_filter);
$total_products = count_products($current_filter_category_id_for_query, $search, $admin_enforced_status_filter, $price_min_filter, $price_max_filter, null, null, $current_featured_param_for_query, $homepage_filter_mode_for_query, $price_type_filter);

if ($debug_items_per_page) {
    error_log("Items per page debug: products returned = " . count($products));
    error_log("Items per page debug: total_products = " . $total_products);

    // Also display debug info on page (remove in production)
    echo "<!-- DEBUG INFO:
    current_items_per_page_value = " . $current_items_per_page_value . "
    limit = " . ($limit ?? 'null') . "
    offset = " . $offset . "
    page = " . $page . "
    products returned = " . count($products) . "
    total_products = " . $total_products . "
    -->";
}

if ($limit !== null && $limit > 0) {
    $total_pages = ceil($total_products / $limit);
} else {
    $total_pages = 1; // Nếu hiển thị tất cả, chỉ có 1 trang
}

// Lấy danh sách danh mục cho bộ lọc (dùng cho dropdown category chung)
$categories = get_categories();

// Hàm trợ giúp để tạo URL sắp xếp
function get_sort_url($column_name, $current_sort_by, $current_sort_order) {
    $base_params = $_GET;
    unset($base_params['sort_by']);
    unset($base_params['sort_order']);

    $new_sort_order = ($current_sort_by === $column_name && $current_sort_order === 'ASC') ? 'DESC' : 'ASC';
    $base_params['sort_by'] = $column_name;
    $base_params['sort_order'] = $new_sort_order;
    return '?' . http_build_query($base_params);
}

// Hàm trợ giúp để lấy class icon sắp xếp
function get_sort_icon_class($column_name, $current_sort_by, $current_sort_order) {
    if ($current_sort_by === $column_name) {
        return $current_sort_order === 'ASC' ? 'fas fa-sort-up' : 'fas fa-sort-down';
    }
    return 'fas fa-sort';
}

// Include header
include_once 'partials/header.php';
?>
<style>
    /* Modern Products Page Styles - Nội Thất Băng Vũ Theme */
    :root {
        /* Primary Colors - Cam chính từ brand */
        --primary: #F37321;
        --primary-dark: #D65A0F;
        --primary-darker: #D35400;
        --primary-light: #FF8A3D;
        --primary-lighter: #FFA66B;
        --primary-lightest: #FFD0AD;
        --primary-ultra-light: #FFF4EC;

        /* Secondary Colors - Xanh đậm */
        --secondary: #2A3B47;
        --secondary-dark: #1E2A32;
        --secondary-light: #435868;

        /* Accent Colors */
        --accent: #4CAF50;
        --accent-dark: #388E3C;
        --accent-light: #81C784;

        /* Status Colors */
        --success: #10B981;
        --warning: #F59E0B;
        --danger: #EF4444;
        --info: #3B82F6;

        /* Gradients với màu brand */
        --primary-gradient: linear-gradient(135deg, #F37321 0%, #D65A0F 100%);
        --secondary-gradient: linear-gradient(135deg, #2A3B47 0%, #1E2A32 100%);
        --success-gradient: linear-gradient(135deg, #10B981 0%, #059669 100%);
        --warning-gradient: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
        --danger-gradient: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
        --info-gradient: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);

        /* Neutral Colors */
        --gray-50: #F9FAFB;
        --gray-100: #F3F4F6;
        --gray-200: #E5E7EB;
        --gray-300: #D1D5DB;
        --gray-400: #9CA3AF;
        --gray-500: #6B7280;
        --gray-600: #4B5563;
        --gray-700: #374151;
        --gray-800: #1F2937;
        --gray-900: #111827;

        /* Shadows */
        --card-shadow: 0 10px 30px rgba(243, 115, 33, 0.08);
        --card-shadow-hover: 0 20px 40px rgba(243, 115, 33, 0.12);
        --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);

        /* Border Radius */
        --border-radius: 1.25rem;
        --border-radius-sm: 0.5rem;
        --border-radius-md: 0.75rem;
        --border-radius-lg: 1rem;

        /* Transitions */
        --transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        --transition-fast: all 0.2s ease;
    }

    /* Products Page Header */
    .products-header {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
        border-radius: 1rem;
        padding: 1.5rem 2rem;
        margin-bottom: 2rem;
        border: 1px solid rgba(243, 115, 33, 0.1);
        position: relative;
        overflow: hidden;
        box-shadow:
            0 10px 15px -3px rgba(0, 0, 0, 0.08),
            0 4px 6px -2px rgba(0, 0, 0, 0.03),
            inset 0 1px 0 rgba(255, 255, 255, 0.6);
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
    }

    .products-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
            radial-gradient(circle at 20% 20%, rgba(243, 115, 33, 0.08) 0%, transparent 40%),
            radial-gradient(circle at 80% 80%, rgba(42, 59, 71, 0.05) 0%, transparent 40%),
            linear-gradient(135deg, rgba(243, 115, 33, 0.02) 0%, transparent 50%);
        pointer-events: none;
        opacity: 0.8;
    }

    .products-header::after {
        content: '';
        position: absolute;
        top: -50%;
        right: -20%;
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, rgba(243, 115, 33, 0.1) 0%, transparent 70%);
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-10px) rotate(5deg); }
    }

    .products-header:hover {
        transform: translateY(-2px);
        box-shadow:
            0 25px 50px -12px rgba(0, 0, 0, 0.15),
            0 20px 25px -5px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
    }

    .products-header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        z-index: 2;
        flex-wrap: wrap;
        gap: 1.5rem;
    }

    .products-title-section {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .products-icon {
        font-size: 1.5rem;
        color: white;
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        padding: 1rem;
        border-radius: 1rem;
        width: 3.5rem;
        height: 3.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        position: relative;
        box-shadow:
            0 6px 10px -2px rgba(243, 115, 33, 0.25),
            0 2px 4px -1px rgba(243, 115, 33, 0.15);
    }

    .products-icon::before {
        content: '';
        position: absolute;
        inset: -3px;
        border-radius: inherit;
        background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary) 100%);
        opacity: 0;
        transition: all 0.4s ease;
        z-index: -1;
    }

    .products-icon::after {
        content: '';
        position: absolute;
        inset: 2px;
        border-radius: calc(1rem - 2px);
        background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 100%);
        pointer-events: none;
    }

    .products-header:hover .products-icon {
        transform: rotate(15deg) scale(1.1);
        box-shadow:
            0 20px 25px -5px rgba(243, 115, 33, 0.4),
            0 10px 10px -5px rgba(243, 115, 33, 0.3);
    }

    .products-header:hover .products-icon::before {
        opacity: 1;
        inset: -5px;
    }

    .products-title-text h1 {
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--gray-800);
        margin: 0 0 0.5rem 0;
        line-height: 1.2;
        letter-spacing: -0.02em;
        background: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-600) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        position: relative;
    }

    .products-title-text h1::before {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 0;
        height: 2px;
        background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);
        border-radius: 1px;
        transition: width 0.6s ease;
    }

    .products-header:hover .products-title-text h1::before {
        width: 100%;
    }

    .products-subtitle {
        color: var(--gray-600);
        font-size: 0.875rem;
        margin: 0;
        font-weight: 400;
        line-height: 1.4;
        opacity: 0.9;
        transition: all 0.3s ease;
    }

    .products-header:hover .products-subtitle {
        color: var(--gray-700);
        opacity: 1;
    }

    .products-actions {
        display: flex;
        align-items: center;
        gap: 1rem;
        position: relative;
        z-index: 2;
    }

    .btn-modern-primary {
        background: var(--primary-gradient);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.75rem;
        font-weight: 600;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: var(--transition);
        text-decoration: none;
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(243, 115, 33, 0.2);
    }

    .btn-modern-primary::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .btn-modern-primary:hover::before {
        left: 100%;
    }

    .btn-modern-primary:hover {
        background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-darker) 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(243, 115, 33, 0.3);
        text-decoration: none;
    }

    .btn-modern-primary i {
        font-size: 1rem;
        transition: transform 0.3s ease;
    }

    .btn-modern-primary:hover i {
        transform: scale(1.1);
    }

    /* Modern Stats Grid - Consistent with dashboard and categories */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.25rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        box-shadow:
            0 10px 15px -3px rgba(0, 0, 0, 0.08),
            0 4px 6px -2px rgba(0, 0, 0, 0.03);
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        position: relative;
        overflow: hidden;
        border: 1px solid rgba(243, 115, 33, 0.08);
    }

    .stat-card:hover {
        transform: translateY(-6px) scale(1.01);
        box-shadow:
            0 20px 25px -5px rgba(0, 0, 0, 0.12),
            0 10px 10px -5px rgba(0, 0, 0, 0.08);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: var(--primary-gradient);
        transition: all 0.4s ease;
        border-radius: 1.5rem 1.5rem 0 0;
    }

    .stat-card::after {
        content: '';
        position: absolute;
        top: -50%;
        right: -30%;
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, rgba(243, 115, 33, 0.05) 0%, transparent 70%);
        border-radius: 50%;
        transition: all 0.4s ease;
    }

    .stat-card:hover::after {
        transform: scale(1.2);
        opacity: 0.8;
    }

    .stat-card.products::before { background: var(--primary-gradient); }
    .stat-card.active::before { background: var(--success-gradient); }
    .stat-card.out-of-stock::before { background: var(--danger-gradient); }
    .stat-card.featured::before { background: var(--warning-gradient); }

    .stat-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
        position: relative;
        z-index: 2;
    }

    .stat-content h3 {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--gray-600);
        margin: 0 0 0.5rem 0;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--gray-800);
        line-height: 1;
        margin-bottom: 0.5rem;
    }

    .stat-change {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .stat-change.positive {
        color: var(--success);
    }

    .stat-change.negative {
        color: var(--danger);
    }

    .stat-change.neutral {
        color: var(--gray-500);
    }

    .stat-change.info {
        color: var(--warning);
    }

    .stat-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 0.75rem;
        background: rgba(243, 115, 33, 0.1);
        color: var(--primary);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        transition: all 0.3s ease;
    }

    .stat-card:hover .stat-icon {
        transform: scale(1.1) rotate(5deg);
        background: rgba(243, 115, 33, 0.15);
    }

    /* Modern Form Controls */
    .form-control-modern {
        border-radius: 0.75rem;
        border: 1px solid var(--gray-200);
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
        transition: var(--transition);
        background: white;
        box-shadow: none;
    }

    .form-control-modern:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.1);
        background: white;
    }

    .form-control-modern::placeholder {
        color: var(--gray-400);
        font-weight: 400;
    }

    /* Modern Filter Card */
    .filter-card {
        background: white;
        border-radius: 1rem;
        box-shadow:
            0 4px 6px -1px rgba(0, 0, 0, 0.1),
            0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border: 1px solid var(--gray-200);
        overflow: hidden;
        margin-bottom: 2rem;
        position: relative;
        transition: all 0.3s ease;
    }

    .filter-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
    }

    .filter-card:hover {
        box-shadow:
            0 10px 15px -3px rgba(0, 0, 0, 0.1),
            0 4px 6px -2px rgba(0, 0, 0, 0.05);
        transform: translateY(-2px);
    }

    .filter-card .card-header {
        padding: 1.5rem 2rem;
        border-bottom: 1px solid var(--gray-200);
        background: linear-gradient(135deg, #fafbfc 0%, #ffffff 100%);
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .filter-card .card-header h6 {
        margin: 0;
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--gray-800);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .filter-card .card-header h6 i {
        color: white;
        font-size: 0.875rem;
        background: var(--primary);
        padding: 0.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 2px 4px rgba(243, 115, 33, 0.2);
    }

    .filter-card .card-body {
        padding: 2rem;
    }

    .filter-card .form-label {
        font-weight: 500;
        color: var(--gray-700);
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .filter-card .form-label i {
        font-size: 0.75rem;
        width: 14px;
        text-align: center;
    }

    .filter-card .form-control-modern {
        height: 42px;
        border: 1px solid var(--gray-300);
        border-radius: 0.5rem;
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        background: white;
    }

    .filter-card .form-control-modern:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.1);
    }

    .filter-card .btn-modern-info {
        background: var(--info);
        color: white;
        border: none;
        padding: 0.625rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 500;
        font-size: 0.875rem;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.2s ease;
        box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
    }

    .filter-card .btn-modern-info:hover {
        background: #2563EB;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
    }

    .filter-card .btn-modern-outline {
        background: white;
        color: var(--gray-600);
        border: 1px solid var(--gray-300);
        padding: 0.625rem 1.25rem;
        border-radius: 0.5rem;
        font-weight: 500;
        font-size: 0.875rem;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.2s ease;
        text-decoration: none;
    }

    .filter-card .btn-modern-outline:hover {
        background: var(--gray-50);
        border-color: var(--gray-400);
        color: var(--gray-700);
        text-decoration: none;
        transform: translateY(-1px);
    }

    /* Filter Form Layout */
    .filter-form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .filter-form-group {
        display: flex;
        flex-direction: column;
    }

    .filter-price-row {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 1.5rem;
        align-items: end;
    }

    .filter-action-row {
        display: flex;
        justify-content: flex-start;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid var(--gray-200);
    }

    .filter-button-group {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    /* Responsive Filter Card */
    @media (max-width: 768px) {
        .filter-card .card-header {
            padding: 1.25rem 1.5rem;
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
        }

        .filter-card .card-body {
            padding: 1.5rem;
        }

        .filter-form-row {
            grid-template-columns: 1fr;
            gap: 1.25rem;
            margin-bottom: 1.25rem;
        }

        .filter-price-row {
            grid-template-columns: 1fr;
            gap: 1.25rem;
        }

        .filter-action-row {
            margin-top: 1.25rem;
            padding-top: 1.25rem;
        }

        .filter-button-group {
            justify-content: stretch;
        }

        .filter-button-group .btn-modern-info {
            width: 100%;
            justify-content: center;
        }
    }

    @media (max-width: 576px) {
        .filter-card .card-header {
            padding: 1rem 1.25rem;
        }

        .filter-card .card-body {
            padding: 1.25rem;
        }

        .filter-card .card-header h6 {
            font-size: 1rem;
        }

        .filter-card .form-control-modern {
            height: 40px;
            font-size: 0.8rem;
        }

        .filter-card .btn-modern-info {
            padding: 0.75rem 1.25rem;
            font-size: 0.8rem;
        }
    }

    /* Modern Table Card Header */
    .table-card-header {
        background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
        border-bottom: 1px solid var(--gray-200);
        padding: 1.5rem 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1.5rem;
        position: relative;
    }

    .table-card-header::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent 0%, var(--primary) 50%, transparent 100%);
        opacity: 0.3;
    }

    .table-header-left {
        flex: 1;
        min-width: 0;
    }

    .table-title-section {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .table-icon {
        width: 48px;
        height: 48px;
        background: var(--primary-gradient);
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.125rem;
        box-shadow:
            0 4px 8px -2px rgba(243, 115, 33, 0.25),
            0 2px 4px -1px rgba(243, 115, 33, 0.15);
        transition: all 0.3s ease;
        position: relative;
    }

    .table-icon::after {
        content: '';
        position: absolute;
        inset: 2px;
        border-radius: calc(0.75rem - 2px);
        background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 100%);
        pointer-events: none;
    }

    .table-card-header:hover .table-icon {
        transform: scale(1.05) rotate(3deg);
        box-shadow:
            0 6px 12px -3px rgba(243, 115, 33, 0.35),
            0 4px 6px -2px rgba(243, 115, 33, 0.25);
    }

    .table-title-content {
        flex: 1;
        min-width: 0;
    }

    .table-title {
        font-size: 1.375rem;
        font-weight: 700;
        color: var(--gray-800);
        margin: 0 0 0.25rem 0;
        line-height: 1.2;
        background: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-600) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .table-subtitle {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: var(--gray-600);
        margin: 0;
    }

    .total-count {
        font-weight: 600;
        color: var(--gray-700);
    }

    .filter-indicator {
        color: var(--primary);
        font-weight: 500;
        background: var(--primary-ultra-light);
        padding: 0.125rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
    }

    .table-header-right {
        flex-shrink: 0;
    }

    .table-controls {
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .control-group {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    /* Items per page form */
    .items-per-page-form {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .control-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--gray-700);
        margin: 0;
        white-space: nowrap;
    }

    .control-select {
        background: white;
        border: 1px solid var(--gray-300);
        border-radius: 0.5rem;
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--gray-700);
        min-width: 80px;
        transition: all 0.2s ease;
        cursor: pointer;
    }

    .control-select:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(243, 115, 33, 0.1);
    }

    .control-select:hover {
        border-color: var(--gray-400);
    }

    /* Bulk actions button */
    .bulk-actions-btn {
        background: white;
        border: 1px solid var(--gray-300);
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--gray-700);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.2s ease;
        cursor: pointer;
        white-space: nowrap;
    }

    .bulk-actions-btn:hover:not(:disabled) {
        background: var(--gray-50);
        border-color: var(--gray-400);
        color: var(--gray-800);
    }

    .bulk-actions-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background: var(--gray-50);
        color: var(--gray-500);
    }

    .bulk-actions-btn i:last-child {
        font-size: 0.75rem;
        transition: transform 0.2s ease;
    }

    .bulk-actions-btn[aria-expanded="true"] i:last-child {
        transform: rotate(180deg);
    }

    /* Dropdown menu styling */
    .table-controls .dropdown-menu {
        border: none;
        border-radius: 0.75rem;
        box-shadow:
            0 10px 15px -3px rgba(0, 0, 0, 0.1),
            0 4px 6px -2px rgba(0, 0, 0, 0.05);
        padding: 0.5rem;
        min-width: 200px;
        margin-top: 0.5rem;
    }

    .table-controls .dropdown-item {
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        transition: all 0.2s ease;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        color: var(--gray-700);
        text-decoration: none;
    }

    .table-controls .dropdown-item:hover {
        background: var(--gray-50);
        color: var(--gray-800);
        transform: translateX(4px);
    }

    .table-controls .dropdown-item i {
        width: 16px;
        text-align: center;
        font-size: 0.875rem;
    }

    /* Responsive Table Header */
    @media (max-width: 768px) {
        .table-card-header {
            padding: 1.25rem 1.5rem;
            flex-direction: column;
            align-items: stretch;
            gap: 1.25rem;
        }

        .table-header-left,
        .table-header-right {
            width: 100%;
        }

        .table-title-section {
            justify-content: center;
            text-align: center;
        }

        .table-title {
            font-size: 1.25rem;
        }

        .table-controls {
            justify-content: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .control-group {
            flex-direction: column;
            align-items: stretch;
            gap: 0.5rem;
        }

        .items-per-page-form {
            justify-content: center;
        }

        .bulk-actions-btn {
            justify-content: center;
            width: 100%;
        }
    }

    @media (max-width: 576px) {
        .table-card-header {
            padding: 1rem 1.25rem;
        }

        .table-icon {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .table-title {
            font-size: 1.125rem;
        }

        .table-subtitle {
            font-size: 0.8rem;
            justify-content: center;
        }

        .control-label,
        .control-select,
        .bulk-actions-btn {
            font-size: 0.8rem;
        }

        .control-select {
            padding: 0.375rem 0.5rem;
        }

        .bulk-actions-btn {
            padding: 0.5rem 0.75rem;
        }

        /* Responsive Table */
        .modern-table-container {
            overflow-x: auto;
        }

        .modern-table {
            min-width: 1000px;
        }

        .modern-table-head th {
            padding: 0.75rem 1rem;
            font-size: 0.8rem;
        }

        .table-row td {
            padding: 0.75rem 1rem;
        }

        .product-image {
            width: 50px;
            height: 50px;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            font-size: 0.8rem;
        }

        .checkbox-label {
            width: 18px;
            height: 18px;
        }

        .checkbox-label:after {
            left: 5px;
            top: 1px;
            width: 5px;
            height: 9px;
        }

        .featured-badge {
            width: 18px;
            height: 18px;
            font-size: 0.55rem;
        }

        .quantity-badge,
        .status-badge,
        .category-badge {
            padding: 0.375rem 0.625rem;
            font-size: 0.75rem;
        }

        .product-name {
            font-size: 0.9rem;
        }

        .product-sku {
            font-size: 0.75rem;
        }

        .current-price,
        .sale-price {
            font-size: 0.9rem;
        }

        .original-price {
            font-size: 0.75rem;
        }

        .date-info {
            font-size: 0.8rem;
        }
    }

    /* Modern Table Styles */
    .table-card-body {
        padding: 0;
        background: white;
        border-radius: 0 0 12px 12px;
    }

    .modern-table-container {
        overflow: hidden;
        border-radius: 0 0 12px 12px;
    }

    .modern-table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
        background: white;
        table-layout: fixed;
    }

    .modern-table-head {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 2px solid var(--orange);
    }

    .modern-table-head th {
        padding: 1rem 0.75rem;
        font-weight: 600;
        font-size: 0.875rem;
        color: var(--gray-700);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: none;
        position: relative;
    }

    .modern-table-head th:first-child {
        padding-left: 1rem;
    }

    .modern-table-head th:last-child {
        padding-right: 1rem;
    }

    /* Column Widths */
    .checkbox-column { width: 40px; text-align: center; }
    .image-column { width: 70px; text-align: center; }
    .product-column { width: 20%; min-width: 180px; max-width: 250px; }
    .category-column { width: 10%; min-width: 100px; }
    .price-column { width: 12%; min-width: 110px; text-align: right; }
    .quantity-column { width: 12%; min-width: 110px; text-align: center; }
    .date-column { width: 10%; min-width: 100px; text-align: center; }
    .status-column { width: 12%; min-width: 110px; text-align: center; }
    .actions-column { width: 12%; min-width: 110px; text-align: center; }

    /* Sort Links */
    .sort-link {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: var(--gray-700);
        text-decoration: none;
        transition: all 0.2s ease;
        padding: 0.25rem 0;
    }

    .sort-link:hover {
        color: var(--orange);
        text-decoration: none;
    }

    .sort-icon {
        margin-left: 0.5rem;
        font-size: 0.75rem;
        opacity: 0.6;
        transition: all 0.2s ease;
    }

    .sort-link:hover .sort-icon {
        opacity: 1;
        color: var(--orange);
    }

    /* Table Body */
    .modern-table-body {
        background: white;
    }

    .table-row {
        border-bottom: 1px solid #f1f3f4;
        transition: all 0.2s ease;
    }

    .table-row:hover {
        background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    }

    .table-row td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
        border: none;
    }

    .table-row td:first-child {
        padding-left: 1rem;
    }

    .table-row td:last-child {
        padding-right: 1rem;
    }

    /* Custom Checkbox */
    .checkbox-wrapper {
        position: relative;
        display: inline-block;
    }

    .modern-checkbox {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
    }

    .checkbox-label {
        position: relative;
        display: inline-block;
        width: 20px;
        height: 20px;
        background: white;
        border: 2px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .checkbox-label:after {
        content: "";
        position: absolute;
        display: none;
        left: 6px;
        top: 2px;
        width: 6px;
        height: 10px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }

    .modern-checkbox:checked + .checkbox-label {
        background: var(--orange);
        border-color: var(--orange);
    }

    .modern-checkbox:checked + .checkbox-label:after {
        display: block;
    }

    .checkbox-label:hover {
        border-color: var(--orange);
        transform: scale(1.05);
    }

    /* Product Image */
    .product-image-wrapper {
        position: relative;
        display: inline-block;
    }

    .product-image {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 6px;
        border: 2px solid #f1f3f4;
        transition: all 0.2s ease;
    }

    .product-image:hover {
        transform: scale(1.05);
        border-color: var(--orange);
        box-shadow: 0 4px 12px rgba(255, 107, 0, 0.2);
    }

    .featured-badge {
        position: absolute;
        top: -6px;
        right: -6px;
        width: 20px;
        height: 20px;
        background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.6rem;
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Product Info */
    .product-info {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .product-name {
        font-weight: 600;
        color: var(--gray-800);
        text-decoration: none;
        line-height: 1.4;
        transition: all 0.2s ease;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 100%;
    }

    .product-name:hover {
        color: var(--orange);
        text-decoration: none;
    }

    .product-sku {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.8rem;
        color: var(--gray-500);
    }

    .product-sku i {
        font-size: 0.75rem;
    }

    /* Category Badge */
    .category-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.375rem 0.5rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 1px solid #dee2e6;
        border-radius: 4px;
        font-size: 0.75rem;
        color: var(--gray-700);
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
    }

    .category-badge i {
        font-size: 0.75rem;
        color: var(--gray-500);
    }

    /* Price Info */
    .price-info {
        text-align: right;
    }

    .current-price {
        font-weight: 700;
        font-size: 1rem;
        color: var(--gray-800);
    }

    .sale-price {
        font-weight: 700;
        font-size: 1rem;
        color: #dc3545;
    }

    .original-price {
        font-size: 0.8rem;
        color: var(--gray-500);
        text-decoration: line-through;
        margin-top: 0.25rem;
    }

    /* Contact Price Badge */
    .contact-price-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
        padding: 0.5rem 0.75rem;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border: 1px solid #2196f3;
        border-radius: 6px;
        color: #1565c0;
        font-size: 0.8rem;
        font-weight: 600;
        white-space: nowrap;
        transition: all 0.2s ease;
    }

    .contact-price-badge:hover {
        background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(33, 150, 243, 0.2);
    }

    .contact-price-badge i {
        font-size: 0.75rem;
        color: #1976d2;
    }

    /* Quantity Badge */
    .quantity-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.375rem 0.5rem;
        border-radius: 16px;
        font-size: 0.75rem;
        font-weight: 600;
        transition: all 0.2s ease;
    }

    .quantity-badge.in-stock {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .quantity-badge.low-stock {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .quantity-badge.out-of-stock {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .quantity-badge i {
        font-size: 0.75rem;
    }

    /* Date Info */
    .date-info {
        font-size: 0.85rem;
        color: var(--gray-600);
        font-weight: 500;
    }

    /* Status Badge */
    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.375rem 0.5rem;
        border-radius: 16px;
        font-size: 0.75rem;
        font-weight: 600;
        transition: all 0.2s ease;
    }

    .status-badge.active {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .status-badge.inactive {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .status-badge.unknown {
        background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
        color: #383d41;
        border: 1px solid #d6d8db;
    }

    .status-badge i {
        font-size: 0.75rem;
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        background: white;
        color: var(--gray-600);
        text-decoration: none;
        transition: all 0.2s ease;
        cursor: pointer;
        font-size: 0.8rem;
    }

    .action-btn:hover {
        text-decoration: none;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .edit-btn:hover {
        background: var(--info);
        color: white;
        border-color: var(--info);
    }

    .delete-btn:hover {
        background: var(--danger);
        color: white;
        border-color: var(--danger);
    }

    .more-btn:hover {
        background: var(--gray-600);
        color: white;
        border-color: var(--gray-600);
    }

    /* Empty State */
    .empty-cell {
        padding: 3rem 1.5rem;
        text-align: center;
        border: none;
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .empty-state-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--gray-400);
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .empty-state-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--gray-700);
        margin: 0;
    }

    .empty-state-text {
        font-size: 0.95rem;
        color: var(--gray-500);
        margin: 0;
        max-width: 400px;
        line-height: 1.5;
    }

    /* Modern Buttons */
    .btn-modern {
        padding: 0.75rem 1.25rem;
        border-radius: 0.75rem;
        font-weight: 500;
        font-size: 0.875rem;
        border: none;
        transition: var(--transition);
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        position: relative;
        overflow: hidden;
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .btn-modern:hover::before {
        left: 100%;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .btn-modern-info {
        background: var(--info-gradient);
        color: white;
        border: none;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
    }

    .btn-modern-info:hover {
        background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
        color: white;
        box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
    }

    .btn-modern-outline {
        background: white;
        color: var(--gray-600);
        border: 1px solid var(--gray-200);
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .btn-modern-outline:hover {
        background: var(--primary-ultra-light);
        border-color: var(--primary-light);
        color: var(--primary-dark);
        box-shadow: 0 4px 12px rgba(243, 115, 33, 0.15);
    }

    .btn-icon-text i {
        font-size: 1rem;
        transition: transform 0.3s ease;
    }

    .btn-icon-text:hover i {
        transform: scale(1.1);
    }

    /* Product Thumbnail */
    .product-thumbnail-modern {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 0.75rem;
        border: 1px solid var(--gray-200);
        transition: var(--transition);
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .product-thumbnail-modern:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    /* Table Actions */
    .table-actions {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        justify-content: center;
    }

    .table-actions .btn {
        width: 32px;
        height: 32px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.5rem;
        transition: var(--transition);
    }

    .table-actions .dropdown-menu {
        min-width: auto;
        border-radius: 0.75rem;
        box-shadow: var(--card-shadow);
        border: none;
        padding: 0.5rem;
    }

    .table-actions .dropdown-item {
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        transition: var(--transition);
        font-size: 0.875rem;
    }

    .table-actions .dropdown-item:hover {
        background: var(--primary-ultra-light);
        color: var(--primary-dark);
        transform: translateX(4px);
    }

    /* Empty State */
    .empty-state {
        padding: 3rem 2rem;
        text-align: center;
    }

    .empty-state-icon {
        font-size: 4rem;
        color: var(--gray-300);
        margin-bottom: 1.5rem;
        opacity: 0.7;
    }

    .empty-state-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--gray-700);
        margin-bottom: 1rem;
    }

    .empty-state-text {
        font-size: 1rem;
        color: var(--gray-500);
        margin-bottom: 2rem;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
        line-height: 1.6;
    }

    /* Modern Pagination */
    .pagination-modern {
        gap: 0.5rem;
    }

    .pagination-modern .page-item .page-link {
        border: 1px solid var(--gray-200);
        border-radius: 0.75rem;
        padding: 0.75rem 1rem;
        color: var(--gray-600);
        font-weight: 500;
        transition: var(--transition);
        background: white;
        margin: 0;
        text-decoration: none;
        min-width: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .pagination-modern .page-item .page-link:hover {
        background: var(--primary-ultra-light);
        border-color: var(--primary-light);
        color: var(--primary-dark);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(243, 115, 33, 0.15);
    }

    .pagination-modern .page-item.active .page-link {
        background: var(--primary-gradient);
        border-color: var(--primary);
        color: white;
        box-shadow: 0 4px 12px rgba(243, 115, 33, 0.2);
    }

    .pagination-modern .page-item.disabled .page-link {
        background: var(--gray-50);
        border-color: var(--gray-200);
        color: var(--gray-400);
        cursor: not-allowed;
    }

    .pagination-modern .page-item.disabled .page-link:hover {
        transform: none;
        box-shadow: none;
        background: var(--gray-50);
        border-color: var(--gray-200);
        color: var(--gray-400);
    }

    .pagination-info {
        font-size: 0.875rem;
        color: var(--gray-600);
        font-weight: 500;
    }

    .pagination-info .text-muted {
        font-size: 0.875rem;
    }

    /* Animation Classes */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.7;
        }
    }

    /* Đã bỏ tất cả animation - hiển thị ngay lập tức */

    /* Enhanced hover effects */
    .products-stat-card:hover .products-stat-icon {
        animation: pulse 1s infinite;
    }

    /* Loading states */
    .loading {
        position: relative;
        pointer-events: none;
    }

    .loading::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        z-index: 10;
        border-radius: inherit;
    }

    .loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 20px;
        height: 20px;
        border: 2px solid var(--gray-200);
        border-top: 2px solid var(--primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        z-index: 11;
    }

    @keyframes spin {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    /* Enhanced table styles */
    .table tbody tr {
        transition: background-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
    }

    /* Ensure table content is immediately visible */
    .modern-table-card .table,
    .modern-table-card .table-responsive,
    .modern-table-card tbody,
    .modern-table-card tr,
    .modern-table-card td {
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* Optimize product images for immediate display */
    .product-image {
        opacity: 1;
        visibility: visible;
        display: block;
        transition: transform 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
    }

    .table tbody tr:hover {
        background: linear-gradient(135deg, var(--primary-ultra-light) 0%, rgba(255,255,255,0.8) 100%);
        transform: translateX(4px);
        box-shadow: 0 4px 12px rgba(243, 115, 33, 0.1);
    }

    /* Enhanced form controls */
    .form-control-modern:hover {
        border-color: var(--primary-light);
        box-shadow: 0 0 0 2px rgba(243, 115, 33, 0.05);
    }

    /* Enhanced badges */
    .badge {
        font-weight: 600;
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        border-radius: 0.5rem;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        transition: var(--transition);
    }

    .badge:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    /* Enhanced status badges */
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        font-size: 0.75rem;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        transition: var(--transition);
    }

    .status-badge:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .status-badge i {
        font-size: 0.875rem;
    }

    .status-badge.pending {
        background: var(--warning-gradient);
        color: white;
    }

    .status-badge.processing {
        background: var(--info-gradient);
        color: white;
    }

    .status-badge.completed {
        background: var(--success-gradient);
        color: white;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .products-header-content {
            flex-direction: column;
            gap: 1.5rem;
        }

        .products-stats {
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        }
    }

    @media (max-width: 768px) {
        .products-header-content {
            flex-direction: column;
            align-items: stretch;
            text-align: center;
        }

        .products-title-section {
            justify-content: center;
        }

        .products-actions {
            justify-content: center;
        }

        .products-stats {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .filter-card .card-header {
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
        }

        .products-header {
            padding: 1.25rem;
        }

        .products-title-text h1 {
            font-size: 1.5rem;
        }

        .products-icon {
            width: 3rem;
            height: 3rem;
            font-size: 1.25rem;
        }

        .table-responsive {
            border-radius: 0.75rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        }

        .table th,
        .table td {
            padding: 0.75rem 0.5rem;
            font-size: 0.875rem;
        }

        .product-thumbnail-modern {
            width: 50px;
            height: 50px;
        }

        .pagination-modern .page-item .page-link {
            padding: 0.5rem 0.75rem;
            min-width: 36px;
        }
    }

    @media (max-width: 576px) {
        .products-stats {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .products-stat-card {
            padding: 1rem;
        }

        .products-stat-value {
            font-size: 1.25rem;
        }

        .products-stat-icon {
            width: 36px;
            height: 36px;
            font-size: 0.875rem;
        }

        .filter-card .card-body {
            padding: 1.25rem;
        }

        .btn-modern {
            padding: 0.625rem 1rem;
            font-size: 0.8rem;
        }

        .table th,
        .table td {
            padding: 0.5rem 0.25rem;
            font-size: 0.8rem;
        }

        .product-thumbnail-modern {
            width: 40px;
            height: 40px;
        }

        .action-button {
            width: 28px;
            height: 28px;
            font-size: 0.75rem;
        }

        .pagination-modern {
            gap: 0.25rem;
        }

        .pagination-modern .page-item .page-link {
            padding: 0.375rem 0.5rem;
            min-width: 32px;
            font-size: 0.8rem;
        }

        .empty-state {
            padding: 2rem 1rem;
        }

        .empty-state-icon {
            font-size: 3rem;
        }

        .empty-state-title {
            font-size: 1.25rem;
        }
    }

    /* Simple Animation for container-fluid - Consistent with categories.php */
    .container-fluid {
        animation: fadeInUp 0.6s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
<!-- Content -->
<div class="container-fluid">
    <!-- Modern Products Header -->
    <div class="products-header">
        <div class="products-header-content">
            <div class="products-title-section">
                <div class="products-icon">
                    <i class="fas fa-box"></i>
                </div>
                <div class="products-title-text">
                    <h1>Quản lý sản phẩm</h1>
                    <div class="products-subtitle">
                        Quản lý toàn bộ sản phẩm trong hệ thống
                    </div>
                </div>
            </div>
            <div class="products-actions">
                <a href="product-add.php" class="btn-modern-primary btn-icon-text">
                    <i class="fas fa-plus"></i>
                    <span>Thêm sản phẩm mới</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Grid -->
    <div class="stats-grid">
        <div class="stat-card products">
            <div class="stat-header">
                <div class="stat-content">
                    <h3>Tổng sản phẩm</h3>
                    <div class="stat-value"><?php echo $total_products_count; ?></div>
                    <div class="stat-change neutral">
                        <i class="fas fa-box"></i>
                        <span>Tất cả sản phẩm</span>
                    </div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-cubes"></i>
                </div>
            </div>
        </div>

        <div class="stat-card active">
            <div class="stat-header">
                <div class="stat-content">
                    <h3>Đang hoạt động</h3>
                    <div class="stat-value"><?php echo $active_products_count; ?></div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i>
                        <span><?php echo $total_products_count > 0 ? round(($active_products_count / $total_products_count) * 100, 1) : 0; ?>% tổng số</span>
                    </div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>

        <div class="stat-card out-of-stock">
            <div class="stat-header">
                <div class="stat-content">
                    <h3>Hết hàng</h3>
                    <div class="stat-value"><?php echo $out_of_stock_count ?? 0; ?></div>
                    <div class="stat-change negative">
                        <i class="fas fa-arrow-down"></i>
                        <span>Cần nhập thêm</span>
                    </div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
        </div>

        <div class="stat-card featured">
            <div class="stat-header">
                <div class="stat-content">
                    <h3>Sản phẩm nổi bật</h3>
                    <div class="stat-value"><?php echo $featured_products_count ?? 0; ?></div>
                    <div class="stat-change info">
                        <i class="fas fa-star"></i>
                        <span>Được đánh dấu</span>
                    </div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-medal"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Thông báo -->
    <?php display_flash_message(); // Quay lại dòng này ?>
    <?php // echo process_and_display_notifications(); // Tạm thời bỏ dòng này ?>

    <!-- Modern Filter Card -->
    <div class="filter-card">
        <div class="card-header">
            <h6><i class="fas fa-filter"></i>Bộ lọc & Tìm kiếm</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="products.php">
                <!-- Main Filter Row -->
                <div class="filter-form-row">
                    <div class="filter-form-group">
                        <label for="search" class="form-label">
                            <i class="fas fa-search text-primary"></i>Tìm kiếm
                        </label>
                        <input type="text" class="form-control form-control-modern" id="search" name="search" placeholder="Nhập tên sản phẩm hoặc SKU..." value="<?php echo htmlspecialchars($search ?? ''); ?>">
                    </div>
                    <div class="filter-form-group">
                        <label for="category_id" class="form-label">
                            <i class="fas fa-folder text-info"></i>Danh mục
                        </label>
                        <select class="form-control form-control-modern" id="category_id" name="category_id">
                            <option value="">-- Tất cả danh mục --</option>
                            <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>" <?php echo ($category_id == $category['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="filter-form-group">
                        <label for="homepage_filter" class="form-label">
                            <i class="fas fa-home text-warning"></i>Hiển thị trang chủ
                        </label>
                        <select class="form-control form-control-modern" id="homepage_filter" name="homepage_filter">
                            <option value="" <?php echo ($homepage_filter_value === '') ? 'selected' : ''; ?>>-- Không lọc --</option>
                            <option value="all_hp" <?php echo ($homepage_filter_value === 'all_hp') ? 'selected' : ''; ?>>Tất cả SP Trang Chủ</option>
                            <option value="featured_hp" <?php echo ($homepage_filter_value === 'featured_hp') ? 'selected' : ''; ?>>Chỉ SP Nổi Bật</option>
                            <?php if (!empty($homepage_display_categories)): ?>
                                <optgroup label="Theo danh mục trang chủ">
                                    <?php foreach ($homepage_display_categories as $hp_category): ?>
                                    <option value="cat_hp_<?php echo $hp_category['id']; ?>" <?php echo ($homepage_filter_value === 'cat_hp_' . $hp_category['id']) ? 'selected' : ''; ?>>
                                        DM: <?php echo htmlspecialchars($hp_category['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </optgroup>
                            <?php endif; ?>
                        </select>
                    </div>
                </div>

                <!-- Price Filter Row -->
                <div class="filter-price-row">
                    <div class="filter-form-group">
                        <label for="price_min" class="form-label">
                            <i class="fas fa-dollar-sign text-success"></i>Giá từ
                        </label>
                        <input type="number" class="form-control form-control-modern" id="price_min" name="price_min" placeholder="Ví dụ: 50000" value="<?php echo htmlspecialchars((string)($price_min_filter ?? '')); ?>" min="0" step="any">
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle"></i>
                            Chỉ áp dụng cho sản phẩm có giá cố định
                        </small>
                    </div>
                    <div class="filter-form-group">
                        <label for="price_max" class="form-label">
                            <i class="fas fa-dollar-sign text-success"></i>Giá đến
                        </label>
                        <input type="number" class="form-control form-control-modern" id="price_max" name="price_max" placeholder="Ví dụ: 1000000" value="<?php echo htmlspecialchars((string)($price_max_filter ?? '')); ?>" min="0" step="any">
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle"></i>
                            Không bao gồm sản phẩm "Liên hệ báo giá"
                        </small>
                    </div>
                    <div class="filter-form-group">
                        <label for="price_type_filter" class="form-label">
                            <i class="fas fa-tags text-info"></i>Loại giá
                        </label>
                        <select class="form-control form-control-modern" id="price_type_filter" name="price_type_filter">
                            <option value="" <?php echo (!isset($_GET['price_type_filter']) || $_GET['price_type_filter'] === '') ? 'selected' : ''; ?>>-- Tất cả loại giá --</option>
                            <option value="fixed" <?php echo (isset($_GET['price_type_filter']) && $_GET['price_type_filter'] === 'fixed') ? 'selected' : ''; ?>>Giá cố định</option>
                            <option value="contact" <?php echo (isset($_GET['price_type_filter']) && $_GET['price_type_filter'] === 'contact') ? 'selected' : ''; ?>>Liên hệ báo giá</option>
                        </select>
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle"></i>
                            Lọc theo cách thức định giá sản phẩm
                        </small>
                    </div>
                </div>

                <!-- Filter Action Row -->
                <div class="filter-action-row">
                    <div class="filter-button-group">
                        <button type="submit" class="btn-modern-info btn-icon-text">
                            <i class="fas fa-search"></i>
                            <span>Áp dụng bộ lọc</span>
                        </button>
                        <?php
                        // Kiểm tra xem có bất kỳ bộ lọc nào đang được áp dụng không (bao gồm cả sắp xếp mặc định)
                        $is_any_filter_applied = $search || $category_id || $price_min_filter || $price_max_filter || $homepage_filter_value !== '' || $current_sort_by !== 'created_at' || $current_sort_order !== 'DESC';
                        if ($is_any_filter_applied):
                        ?>
                        <a href="products.php" class="btn-modern-outline btn-icon-text" title="Đặt lại bộ lọc và sắp xếp">
                            <i class="fas fa-undo"></i>
                            <span>Đặt lại</span>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Modern Products Table -->
    <div class="modern-table-card">
        <div class="table-card-header">
            <div class="table-header-left">
                <div class="table-title-section">
                    <div class="table-icon">
                        <i class="fas fa-list"></i>
                    </div>
                    <div class="table-title-content">
                        <h2 class="table-title">Danh sách sản phẩm</h2>
                        <div class="table-subtitle">
                            <span class="total-count"><?php echo $total_products; ?> sản phẩm</span>
                            <?php if ($search || $category_id || $homepage_filter_value): ?>
                                <span class="filter-indicator">• Đã lọc</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-header-right">
                <div class="table-controls">
                    <!-- Items per page -->
                    <div class="control-group">
                        <form method="GET" action="products.php" class="items-per-page-form" id="itemsPerPageForm">
                            <?php foreach ($_GET as $key => $value): ?>
                                <?php if ($key !== 'items_per_page' && $key !== 'page'): ?>
                                    <input type="hidden" name="<?php echo htmlspecialchars($key); ?>" value="<?php echo htmlspecialchars($value); ?>">
                                <?php endif; ?>
                            <?php endforeach; ?>
                            <label for="items_per_page_select" class="control-label">Hiển thị:</label>
                            <select name="items_per_page" id="items_per_page_select" class="control-select">
                                <?php
                                $options_items_per_page = [10, 20, 50, 100, 200, 'all'];
                                $options_labels = [10 => '10', 20 => '20', 50 => '50', 100 => '100', 200 => '200', 'all' => 'Tất cả'];
                                foreach ($options_items_per_page as $option_val):
                                ?>
                                <option value="<?php echo $option_val; ?>" <?php echo ($current_items_per_page_value == $option_val) ? 'selected' : ''; ?>>
                                    <?php echo $options_labels[$option_val]; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </form>
                    </div>

                    <!-- Bulk actions -->
                    <div class="control-group">
                        <div class="dropdown">
                            <button class="bulk-actions-btn" type="button" id="bulkActionsDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" disabled>
                                <i class="fas fa-tasks"></i>
                                <span>Hành động hàng loạt</span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="dropdown-menu dropdown-menu-right" aria-labelledby="bulkActionsDropdown">
                                <a class="dropdown-item" href="#" onclick="bulkAction('delete')">
                                    <i class="fas fa-trash-alt text-danger"></i>
                                    <span>Xóa đã chọn</span>
                                </a>
                                <a class="dropdown-item" href="#" onclick="bulkAction('publish')">
                                    <i class="fas fa-eye text-success"></i>
                                    <span>Hiển thị đã chọn</span>
                                </a>
                                <a class="dropdown-item" href="#" onclick="bulkAction('unpublish')">
                                    <i class="fas fa-eye-slash text-warning"></i>
                                    <span>Ẩn đã chọn</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="table-card-body">
            <form id="bulkActionForm" method="POST" action="product-bulk-actions.php">
                <input type="hidden" name="action" id="bulk_action_name">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

                <div class="modern-table-container">
                    <table class="modern-table">
                        <thead class="modern-table-head">
                            <tr>
                                <th class="checkbox-column">
                                    <div class="checkbox-wrapper">
                                        <input type="checkbox" id="selectAll" class="modern-checkbox">
                                        <label for="selectAll" class="checkbox-label"></label>
                                    </div>
                                </th>
                                <th class="image-column">Hình ảnh</th>
                                <th class="product-column">
                                    <a href="<?php echo get_sort_url('name', $current_sort_by, $current_sort_order); ?>" class="sort-link">
                                        <span>Tên sản phẩm</span>
                                        <i class="<?php echo get_sort_icon_class('name', $current_sort_by, $current_sort_order); ?> sort-icon"></i>
                                    </a>
                                </th>
                                <th class="category-column">Danh mục</th>
                                <th class="price-column">
                                    <a href="<?php echo get_sort_url('price', $current_sort_by, $current_sort_order); ?>" class="sort-link">
                                        <span>Giá</span>
                                        <i class="<?php echo get_sort_icon_class('price', $current_sort_by, $current_sort_order); ?> sort-icon"></i>
                                    </a>
                                </th>
                                <th class="quantity-column">
                                    <a href="<?php echo get_sort_url('quantity', $current_sort_by, $current_sort_order); ?>" class="sort-link">
                                        <span>Số lượng</span>
                                        <i class="<?php echo get_sort_icon_class('quantity', $current_sort_by, $current_sort_order); ?> sort-icon"></i>
                                    </a>
                                </th>
                                <th class="date-column">
                                    <a href="<?php echo get_sort_url('created_at', $current_sort_by, $current_sort_order); ?>" class="sort-link">
                                        <span>Ngày tạo</span>
                                        <i class="<?php echo get_sort_icon_class('created_at', $current_sort_by, $current_sort_order); ?> sort-icon"></i>
                                    </a>
                                </th>
                                <th class="status-column">Trạng thái</th>
                                <th class="actions-column">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody class="modern-table-body">
                            <?php if (count($products) > 0): ?>
                            <?php foreach ($products as $product): ?>
                            <tr class="table-row">
                                <td class="checkbox-column">
                                    <div class="checkbox-wrapper">
                                        <input type="checkbox" name="product_ids[]" value="<?php echo $product['id']; ?>" class="select-item modern-checkbox" id="product_<?php echo $product['id']; ?>">
                                        <label for="product_<?php echo $product['id']; ?>" class="checkbox-label"></label>
                                    </div>
                                </td>
                                <td class="image-column">
                                    <div class="product-image-wrapper">
                                        <?php if (!empty($product['image'])): ?>
                                        <img src="<?php echo BASE_URL . '/uploads/products/' . $product['image']; ?>" alt="<?php echo htmlspecialchars($product['name']); ?>" class="product-image">
                                        <?php else: ?>
                                        <img src="<?php echo BASE_URL; ?>/assets/img/no-image.jpg" alt="No Image" class="product-image">
                                        <?php endif; ?>
                                        <?php if ($product['featured'] == 1): ?>
                                        <div class="featured-badge">
                                            <i class="fas fa-star"></i>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="product-column">
                                    <div class="product-info">
                                        <a href="product-edit.php?id=<?php echo $product['id']; ?><?php echo $category_id ? '&category_id=' . $category_id : ''; ?>" class="product-name">
                                            <?php echo htmlspecialchars($product['name']); ?>
                                        </a>
                                        <div class="product-sku">
                                            <i class="fas fa-barcode"></i>
                                            <span>SKU: <?php echo htmlspecialchars($product['sku'] ?? 'N/A'); ?></span>
                                        </div>
                                    </div>
                                </td>
                                <td class="category-column">
                                    <div class="category-badge">
                                        <i class="fas fa-folder"></i>
                                        <span><?php echo htmlspecialchars($product['category_name'] ?? 'N/A'); ?></span>
                                    </div>
                                </td>
                                <td class="price-column">
                                    <div class="price-info">
                                        <?php if ($product['price_type'] === 'contact'): ?>
                                            <div class="contact-price-badge">
                                                <i class="fas fa-phone-alt"></i>
                                                <span>Liên hệ báo giá</span>
                                            </div>
                                        <?php else: ?>
                                            <?php if (!empty($product['sale_price']) && $product['sale_price'] < $product['price']): ?>
                                                <div class="sale-price"><?php echo format_currency($product['sale_price']); ?></div>
                                                <div class="original-price"><?php echo format_currency($product['price']); ?></div>
                                            <?php else: ?>
                                                <div class="current-price"><?php echo format_currency($product['price']); ?></div>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="quantity-column">
                                    <div class="quantity-badge <?php echo $product['quantity'] <= 0 ? 'out-of-stock' : ($product['quantity'] <= 5 ? 'low-stock' : 'in-stock'); ?>">
                                        <?php if ($product['quantity'] <= 0): ?>
                                            <i class="fas fa-times-circle"></i>
                                            <span>Hết hàng</span>
                                        <?php elseif ($product['quantity'] <= 5): ?>
                                            <i class="fas fa-exclamation-triangle"></i>
                                            <span><?php echo $product['quantity']; ?></span>
                                        <?php else: ?>
                                            <i class="fas fa-check-circle"></i>
                                            <span><?php echo $product['quantity']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="date-column">
                                    <div class="date-info">
                                        <?php echo !empty($product['created_at']) ? date('d/m/Y', strtotime($product['created_at'])) : 'N/A'; ?>
                                    </div>
                                </td>
                                <td class="status-column">
                                    <?php if ($product['status'] == 1): ?>
                                    <div class="status-badge active">
                                        <i class="fas fa-check-circle"></i>
                                        <span>Hoạt động</span>
                                    </div>
                                    <?php elseif ($product['status'] == 0): ?>
                                    <div class="status-badge inactive">
                                        <i class="fas fa-pause-circle"></i>
                                        <span>Ngừng KD</span>
                                    </div>
                                    <?php else: ?>
                                    <div class="status-badge unknown">
                                        <i class="fas fa-question-circle"></i>
                                        <span>Khác</span>
                                    </div>
                                    <?php endif; ?>
                                </td>
                                <td class="actions-column">
                                    <div class="action-buttons">
                                        <a href="product-edit.php?id=<?php echo $product['id']; ?><?php echo $category_id ? '&category_id=' . $category_id : ''; ?>" class="action-btn edit-btn" title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="product-delete.php?id=<?php echo $product['id']; ?><?php echo $category_id ? '&category_id=' . $category_id : ''; ?>" class="action-btn delete-btn" title="Xóa" onclick="return confirm('Bạn có chắc chắn muốn xóa sản phẩm này? Thao tác này không thể hoàn tác.');">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                        <div class="dropdown">
                                            <button class="action-btn more-btn" type="button" id="dropdownMenuButton_<?php echo $product['id']; ?>" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" title="Thêm tùy chọn">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton_<?php echo $product['id']; ?>">
                                                <a class="dropdown-item" href="product_actions.php?action=duplicate&id=<?php echo $product['id']; ?>&token=<?php echo generate_csrf_token(); ?>" onclick="return confirm('Bạn có chắc muốn nhân bản sản phẩm này? Sản phẩm mới sẽ được tạo ở trạng thái ẩn.')">
                                                    <i class="fas fa-copy text-info"></i>
                                                    <span>Nhân bản</span>
                                                </a>
                                                <a class="dropdown-item" href="product_actions.php?action=toggle_featured&id=<?php echo $product['id']; ?>&token=<?php echo generate_csrf_token(); ?>">
                                                    <i class="fas fa-star <?php echo $product['featured'] == 1 ? 'text-warning' : 'text-gray-400'; ?>"></i>
                                                    <span><?php echo $product['featured'] == 1 ? 'Bỏ nổi bật' : 'Đánh dấu nổi bật'; ?></span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php else: ?>
                            <tr class="empty-row">
                                <td colspan="9" class="empty-cell">
                                    <div class="empty-state">
                                        <div class="empty-state-icon">
                                            <i class="fas fa-box-open"></i>
                                        </div>
                                        <h4 class="empty-state-title">Không tìm thấy sản phẩm nào</h4>
                                        <p class="empty-state-text">
                                            <?php if ($search || $category_id || $homepage_filter_value): ?>
                                                Thử điều chỉnh bộ lọc hoặc tìm kiếm với từ khóa khác
                                            <?php else: ?>
                                                Hãy thêm sản phẩm đầu tiên vào hệ thống
                                            <?php endif; ?>
                                        </p>
                                        <?php if (!$search && !$category_id && !$homepage_filter_value): ?>
                                            <a href="product-add.php" class="btn-modern-primary btn-icon-text">
                                                <i class="fas fa-plus"></i>
                                                <span>Thêm sản phẩm đầu tiên</span>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </form>

            <!-- Modern Pagination -->
            <?php if ($total_pages > 1 && $current_items_per_page_value !== 'all'): ?>
            <div class="d-flex justify-content-between align-items-center mt-4 pt-3 border-top">
                <div class="pagination-info">
                    <small class="text-muted">
                        Hiển thị <?php echo (($page - 1) * $limit) + 1; ?> - <?php echo min($page * $limit, $total_products); ?>
                        trong tổng số <?php echo $total_products; ?> sản phẩm
                    </small>
                </div>
                <nav aria-label="Page navigation">
                    <ul class="pagination pagination-modern mb-0">
                        <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                <i class="fas fa-chevron-left"></i>
                                <span class="d-none d-sm-inline ms-1">Trước</span>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php
                        $start_page = max(1, $page - 2);
                        $end_page = min($total_pages, $page + 2);

                        if ($start_page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>">1</a>
                            </li>
                            <?php if ($start_page > 2): ?>
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                        <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                        <?php endfor; ?>

                        <?php if ($end_page < $total_pages): ?>
                            <?php if ($end_page < $total_pages - 1): ?>
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            <?php endif; ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $total_pages])); ?>"><?php echo $total_pages; ?></a>
                            </li>
                        <?php endif; ?>

                        <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                <span class="d-none d-sm-inline me-1">Tiếp</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// Include footer
include_once 'partials/footer.php';
?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const itemCheckboxes = document.querySelectorAll('.select-item');
    const bulkActionsButton = document.getElementById('bulkActionsDropdown');

    function toggleBulkActionsButton() {
        let checkedCount = 0;
        itemCheckboxes.forEach(checkbox => {
            if (checkbox.checked) {
                checkedCount++;
            }
        });
        bulkActionsButton.disabled = checkedCount === 0;
    }

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            itemCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
            toggleBulkActionsButton();
        });
    }

    itemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if (!checkbox.checked) {
                selectAllCheckbox.checked = false;
            }
            toggleBulkActionsButton();
        });
    });

    // Initial check
    toggleBulkActionsButton();

    // Initialize Smart Auto-Scroll Feature
    initSmartAutoScroll();

    // Initialize Page Animations - COMPLETELY DISABLED to prevent conflict with CSS animation
    // initPageAnimations();

    // Initialize Table Content Optimization
    initTableContentOptimization();

    // No JavaScript animation - using pure CSS animation only
});

function bulkAction(actionName) {
    const form = document.getElementById('bulkActionForm');
    const actionInput = document.getElementById('bulk_action_name');
    const selectedItems = document.querySelectorAll('.select-item:checked');

    if (selectedItems.length === 0) {
        alert('Vui lòng chọn ít nhất một sản phẩm để thực hiện hành động.');
        return;
    }

    if (actionInput && form) {
        actionInput.value = actionName;
        let confirmationMessage = 'Bạn có chắc chắn muốn thực hiện hành động này với các sản phẩm đã chọn?';
        if (actionName === 'delete') {
            confirmationMessage = 'Bạn có chắc chắn muốn XÓA các sản phẩm đã chọn? Hành động này không thể hoàn tác.';
        }
        if (confirm(confirmationMessage)) {
            // Set flag for auto-scroll after bulk action
            sessionStorage.setItem('autoScrollAfterAction', 'true');
            form.submit();
        }
    }
}

// JavaScript cho items_per_page và localStorage
document.addEventListener('DOMContentLoaded', function() {
    const itemsPerPageSelect = document.getElementById('items_per_page_select');
    const itemsPerPageForm = document.getElementById('itemsPerPageForm');
    const localStorageKey = 'adminProductsItemsPerPage';
    const allowedItemsPerPageValues = <?php echo json_encode($allowed_items_per_page); ?>; // Lấy từ PHP
    const debugItemsPerPage = true; // Set to false in production

    // Create a more flexible allowed values array that includes both string and number versions
    const allowedValuesFlexible = [];
    allowedItemsPerPageValues.forEach(val => {
        allowedValuesFlexible.push(val);
        if (typeof val === 'number') {
            allowedValuesFlexible.push(val.toString());
        } else if (typeof val === 'string' && !isNaN(val)) {
            allowedValuesFlexible.push(parseInt(val));
        }
    });

    if (debugItemsPerPage) {
        console.log('Items per page initialization:', {
            selectElement: !!itemsPerPageSelect,
            formElement: !!itemsPerPageForm,
            allowedValues: allowedItemsPerPageValues,
            allowedValuesFlexible: allowedValuesFlexible
        });
    }

    // 1. Khi trang tải, kiểm tra localStorage và URL
    const storedItemsPerPage = localStorage.getItem(localStorageKey);
    const urlParams = new URLSearchParams(window.location.search);
    const itemsPerPageFromUrl = urlParams.get('items_per_page');

    if (debugItemsPerPage) {
        console.log('Items per page check:', {
            storedValue: storedItemsPerPage,
            urlValue: itemsPerPageFromUrl,
            currentSelectValue: itemsPerPageSelect ? itemsPerPageSelect.value : 'N/A'
        });
    }

    // DISABLED: Auto-redirect logic that causes animation issues and unnecessary URL parameters
    // if (storedItemsPerPage && allowedValuesFlexible.includes(storedItemsPerPage)) {
    //     if (!itemsPerPageFromUrl || itemsPerPageFromUrl !== storedItemsPerPage) {
    //         // Nếu có giá trị trong localStorage hợp lệ và nó khác với URL (hoặc URL không có)
    //         // -> ưu tiên localStorage bằng cách cập nhật URL và tải lại.
    //         if (debugItemsPerPage) {
    //             console.log('Redirecting to apply stored items per page:', storedItemsPerPage);
    //         }
    //         urlParams.set('items_per_page', storedItemsPerPage);
    //         // Khi thay đổi items_per_page, nên reset về trang 1
    //         urlParams.set('page', '1');
    //         window.location.href = window.location.pathname + '?' + urlParams.toString();
    //         return; // Dừng thực thi script tiếp theo vì trang sẽ tải lại
    //     }
    // }

    // 2. Khi người dùng thay đổi lựa chọn trong dropdown
    if (itemsPerPageSelect && itemsPerPageForm) {
        itemsPerPageSelect.addEventListener('change', function(e) {
            const selectedValue = this.value;
            const previousValue = this.getAttribute('data-previous-value') || this.defaultValue;

            if (debugItemsPerPage) {
                console.log('Items per page changed:', {
                    previousValue: previousValue,
                    newValue: selectedValue,
                    isValid: allowedValuesFlexible.includes(selectedValue),
                    allowedValues: allowedItemsPerPageValues,
                    allowedValuesFlexible: allowedValuesFlexible,
                    selectElement: this,
                    formElement: itemsPerPageForm
                });
            }

            // Validate selected value using flexible array
            if (!allowedValuesFlexible.includes(selectedValue)) {
                console.error('Invalid items per page value:', selectedValue, 'Allowed:', allowedValuesFlexible);
                return;
            }

            // Save to localStorage
            localStorage.setItem(localStorageKey, selectedValue);
            if (debugItemsPerPage) {
                console.log('Saved to localStorage:', selectedValue);
            }

            // Set flag for auto-scroll after items per page change
            sessionStorage.setItem('autoScrollAfterAction', 'true');

            // Đảm bảo page được reset về 1 khi items_per_page thay đổi
            let pageInput = itemsPerPageForm.querySelector('input[name="page"]');
            if (!pageInput) {
                pageInput = document.createElement('input');
                pageInput.type = 'hidden';
                pageInput.name = 'page';
                itemsPerPageForm.appendChild(pageInput);
            }
            pageInput.value = '1';

            // Update the select element's data attribute for next change
            this.setAttribute('data-previous-value', selectedValue);

            if (debugItemsPerPage) {
                console.log('Submitting items per page form...');
                console.log('Form action:', itemsPerPageForm.action);
                console.log('Form method:', itemsPerPageForm.method);

                // Log all form data before submission
                const formData = new FormData(itemsPerPageForm);
                console.log('Form data before submission:');
                for (let [key, value] of formData.entries()) {
                    console.log(`  ${key}: ${value}`);
                }

                // Also log the select element's current value
                console.log('Select element value at submission:', this.value);
                console.log('Select element name:', this.name);
            }

            // Small delay to ensure all data is set before submission
            setTimeout(() => {
                if (debugItemsPerPage) {
                    console.log('Actually submitting form now...');
                }
                itemsPerPageForm.submit();
            }, 100);
        });

        if (debugItemsPerPage) {
            console.log('Items per page event listener attached successfully');

            // Add debug helper functions to window for manual testing
            window.debugItemsPerPage = {
                clearLocalStorage: function() {
                    localStorage.removeItem(localStorageKey);
                    console.log('Cleared items per page localStorage');
                },
                getCurrentValue: function() {
                    return {
                        localStorage: localStorage.getItem(localStorageKey),
                        selectValue: itemsPerPageSelect ? itemsPerPageSelect.value : 'N/A',
                        urlParam: new URLSearchParams(window.location.search).get('items_per_page')
                    };
                },
                forceSubmit: function(value) {
                    if (itemsPerPageSelect && itemsPerPageForm) {
                        itemsPerPageSelect.value = value;
                        localStorage.setItem(localStorageKey, value);
                        itemsPerPageForm.submit();
                    }
                }
            };

            console.log('Debug helper functions added to window.debugItemsPerPage');
            console.log('Available functions: clearLocalStorage(), getCurrentValue(), forceSubmit(value)');
        }
    } else {
        if (debugItemsPerPage) {
            console.error('Items per page elements not found:', {
                select: !!itemsPerPageSelect,
                form: !!itemsPerPageForm
            });
        }
    }
});

// Smart Auto-Scroll Implementation
function initSmartAutoScroll() {
    // Setup global sidebar navigation tracking
    setupGlobalSidebarTracking();

    // Check if we should auto-scroll
    if (shouldAutoScroll()) {
        performSmartScroll();
        // Clear the flag after scrolling
        sessionStorage.removeItem('autoScrollAfterAction');
        // Clean up URL by removing auto_scroll parameter
        cleanUpAutoScrollParam();
    }

    // Add event listeners for future actions
    setupAutoScrollTriggers();
}

// Animation Management System
function initPageAnimations() {
    const debugAnimations = false; // Set to false in production

    // Use a more reliable method to detect if animations should play
    // Check if this is a fresh page load or navigation
    const shouldAnimate = shouldPlayAnimations();

    if (debugAnimations) {
        console.log('Animation decision:', {
            shouldAnimate,
            currentUrl: window.location.href,
            referrer: document.referrer,
            timestamp: Date.now()
        });
    }

    // Đã bỏ tất cả animation - chỉ đảm bảo table hiển thị
    ensureTableContentVisible();
}

// Đã xóa bỏ logic animation phức tạp - giờ dùng CSS animation tự động như trang tổng quan

function detectFreshNavigationForAnimations() {
    // Similar to auto-scroll detection but focused on animation needs

    // No referrer = direct access or bookmark = animate
    if (!document.referrer) {
        return true;
    }

    // Same page refresh = animate
    if (document.referrer === window.location.href) {
        return true;
    }

    // Check if we came from a different admin page (sidebar navigation)
    try {
        const referrerUrl = new URL(document.referrer);
        const currentUrl = new URL(window.location.href);

        // If referrer is from different admin page, animate
        if (referrerUrl.pathname.includes('/admin/') &&
            referrerUrl.pathname !== currentUrl.pathname) {
            return true;
        }

        // If same page but significantly different parameters, animate
        if (referrerUrl.pathname === currentUrl.pathname) {
            const referrerParams = referrerUrl.search;
            const currentParams = currentUrl.search;

            // If going from filtered page to clean page (sidebar click), animate
            if (referrerParams && !currentParams) {
                return true;
            }

            // If parameters are very different, animate
            if (referrerParams !== currentParams) {
                const referrerParamCount = (referrerParams.match(/&/g) || []).length;
                const currentParamCount = (currentParams.match(/&/g) || []).length;

                // If parameter count difference is significant, animate
                if (Math.abs(referrerParamCount - currentParamCount) > 1) {
                    return true;
                }
            }
        }
    } catch (e) {
        // If URL parsing fails, animate to be safe
        return true;
    }

    // Check for explicit action flags that indicate form submission
    const hasActionFlag = sessionStorage.getItem('autoScrollAfterAction') === 'true';
    if (hasActionFlag) {
        return false; // Don't animate for form actions
    }

    // Default to animating for safety
    return true;
}

function startPageAnimations() {
    // Đã bỏ tất cả animation - không cần xử lý gì
    ensureTableContentVisible();
}

// Function đã được đơn giản hóa - không cần thiết nữa

function ensureTableContentVisible() {
    // Force immediate visibility of table content elements
    const tableElements = document.querySelectorAll('.modern-table-card .table, .modern-table-card .table-responsive, .table tbody tr');
    tableElements.forEach(element => {
        element.style.opacity = '1';
        element.style.visibility = 'visible';
        element.style.transform = 'none';
        element.style.transition = 'none';
    });

    // Force immediate visibility of product images
    const productImages = document.querySelectorAll('.product-image');
    productImages.forEach(img => {
        img.style.opacity = '1';
        img.style.visibility = 'visible';
        img.style.display = 'block';

        // Handle image loading
        if (!img.complete) {
            img.onload = function() {
                this.style.opacity = '1';
                this.style.visibility = 'visible';
            };
        }
    });

    // Re-enable transitions after content is visible
    setTimeout(() => {
        tableElements.forEach(element => {
            element.style.transition = '';
        });
    }, 100);
}

// Table Content Optimization System
function initTableContentOptimization() {
    // Optimize product images loading
    optimizeProductImages();

    // Ensure table is ready for display
    prepareTableForDisplay();

    // Handle any loading states
    clearLoadingStates();
}

function optimizeProductImages() {
    const productImages = document.querySelectorAll('.product-image');

    productImages.forEach(img => {
        // Set default styles to prevent layout shifts
        img.style.width = '50px';
        img.style.height = '50px';
        img.style.objectFit = 'cover';
        img.style.display = 'block';

        // Handle loading states
        if (img.complete) {
            // Image already loaded
            img.style.opacity = '1';
            img.style.visibility = 'visible';
        } else {
            // Image still loading - show placeholder
            img.style.opacity = '0.7';
            img.style.background = '#f8f9fa';

            img.onload = function() {
                this.style.opacity = '1';
                this.style.visibility = 'visible';
                this.style.background = '';
            };

            img.onerror = function() {
                // Handle broken images
                this.style.opacity = '1';
                this.style.background = '#f8f9fa url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'24\' height=\'24\' viewBox=\'0 0 24 24\' fill=\'%23dee2e6\'%3E%3Cpath d=\'M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z\'/%3E%3C/svg%3E") center/24px no-repeat';
            };
        }
    });
}

function prepareTableForDisplay() {
    // Remove any initial hiding or animation delays from table elements
    const tableContainer = document.querySelector('.modern-table-card');
    if (tableContainer) {
        // Ensure table container is ready
        tableContainer.style.minHeight = 'auto';

        // Ensure table content is visible
        const tableContent = tableContainer.querySelectorAll('.table, .table-responsive, tbody, tr, td');
        tableContent.forEach(element => {
            element.style.visibility = 'visible';
            element.style.opacity = '1';
        });
    }
}

function clearLoadingStates() {
    // Remove any loading classes or states that might delay display
    const loadingElements = document.querySelectorAll('.loading, .spinner, .skeleton');
    loadingElements.forEach(element => {
        element.classList.remove('loading', 'spinner', 'skeleton');
    });

    // Clear any loading overlays
    const loadingOverlays = document.querySelectorAll('#loading-overlay, .loading-overlay');
    loadingOverlays.forEach(overlay => {
        overlay.style.display = 'none';
    });
}

// Global function to track sidebar navigation across all admin pages
function setupGlobalSidebarTracking() {
    const debugAutoScroll = false; // Match the debug setting

    // Use event delegation to catch all sidebar clicks, even if elements are added later
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        sidebar.addEventListener('click', function(e) {
            // Check if clicked element is a navigation link
            const clickedLink = e.target.closest('.nav-link, .sidebar-brand');
            if (clickedLink) {
                sessionStorage.setItem('navigationSource', 'sidebar');
                if (debugAutoScroll) {
                    console.log('Sidebar navigation marked via delegation for:', clickedLink.href || clickedLink.textContent.trim());
                }
            }
        });

        if (debugAutoScroll) {
            console.log('Sidebar event delegation setup completed');
        }
    }

    // Track all sidebar navigation links (backup method)
    const sidebarLinks = document.querySelectorAll('.sidebar .nav-link');
    if (debugAutoScroll) {
        console.log('Setting up sidebar tracking for', sidebarLinks.length, 'links');
    }

    sidebarLinks.forEach((link, index) => {
        if (debugAutoScroll) {
            console.log(`Sidebar link ${index}:`, link.href, link.textContent.trim());
        }

        link.addEventListener('click', function(e) {
            // Mark this as sidebar navigation for the next page
            sessionStorage.setItem('navigationSource', 'sidebar');
            if (debugAutoScroll) {
                console.log('Sidebar navigation marked for:', this.href, 'Text:', this.textContent.trim());
            }
        });
    });

    // Also track sidebar brand/logo clicks
    const sidebarBrand = document.querySelector('.sidebar-brand');
    if (sidebarBrand) {
        if (debugAutoScroll) {
            console.log('Setting up sidebar brand tracking:', sidebarBrand.href);
        }
        sidebarBrand.addEventListener('click', function() {
            sessionStorage.setItem('navigationSource', 'sidebar');
            if (debugAutoScroll) {
                console.log('Sidebar brand navigation marked');
            }
        });
    }

    // Additional specific tracking for products link
    const productsLink = document.querySelector('.sidebar a[href*="products.php"]');
    if (productsLink && debugAutoScroll) {
        console.log('Products link found in sidebar:', productsLink.href);
    }
}

function shouldAutoScroll() {
    // Check for URL parameters that indicate an action was performed
    const urlParams = new URLSearchParams(window.location.search);

    // Check for explicit auto_scroll parameter (from CRUD operations)
    const hasAutoScrollParam = urlParams.has('auto_scroll');

    // Check for session storage flag (set by bulk actions or CRUD operations)
    const hasActionFlag = sessionStorage.getItem('autoScrollAfterAction') === 'true';

    // Check if we came from a form submission (referrer check)
    const cameFromFormAction = document.referrer &&
                              (document.referrer.includes('product-add.php') ||
                               document.referrer.includes('product-edit.php') ||
                               document.referrer.includes('product-delete.php') ||
                               document.referrer.includes('product_actions.php') ||
                               document.referrer.includes('product-bulk-actions.php'));

    // Check for filter parameters (only if not a fresh navigation)
    const hasFilterParams = urlParams.has('search') || urlParams.has('category_id') ||
                           urlParams.has('price_min') || urlParams.has('price_max') ||
                           urlParams.has('homepage_filter');

    // Check for sort parameters (only if not a fresh navigation)
    const hasSortParams = urlParams.has('sort_by') || urlParams.has('sort_order');

    // Check for pagination parameters (only if not a fresh navigation)
    const hasPaginationParams = urlParams.has('page') || urlParams.has('items_per_page');

    // Detect if this is a fresh navigation (sidebar click, direct URL access, refresh)
    const isFreshNavigation = detectFreshNavigation();

    // Debug logging (can be disabled in production)
    const debugAutoScroll = false; // Set to true for debugging
    if (debugAutoScroll) {
        console.log('Auto-scroll debug:', {
            hasAutoScrollParam,
            hasActionFlag,
            cameFromFormAction,
            hasFilterParams,
            hasSortParams,
            hasPaginationParams,
            isFreshNavigation,
            referrer: document.referrer,
            currentUrl: window.location.href
        });
    }

    // If it's a fresh navigation, only auto-scroll for explicit flags or CRUD operations
    if (isFreshNavigation) {
        const shouldScroll = hasAutoScrollParam || hasActionFlag || cameFromFormAction;
        if (debugAutoScroll) {
            console.log('Fresh navigation detected, should auto-scroll:', shouldScroll);
        }
        return shouldScroll;
    }

    // For non-fresh navigation, check all action indicators
    const shouldScroll = hasAutoScrollParam || hasFilterParams || hasSortParams || hasPaginationParams || hasActionFlag || cameFromFormAction;
    if (debugAutoScroll) {
        console.log('Non-fresh navigation, should auto-scroll:', shouldScroll);
    }
    return shouldScroll;
}

function detectFreshNavigation() {
    const debugAutoScroll = false; // Match the debug setting

    // Check if this is a fresh page load (no referrer or referrer is different domain)
    if (!document.referrer) {
        if (debugAutoScroll) console.log('Fresh navigation: No referrer');
        return true;
    }

    // Check if we came from the same page (refresh)
    if (document.referrer === window.location.href) {
        if (debugAutoScroll) console.log('Fresh navigation: Same page refresh');
        return true;
    }

    // Check if navigation source was marked as sidebar
    const navigationSource = sessionStorage.getItem('navigationSource');
    if (debugAutoScroll) {
        console.log('Navigation source from sessionStorage:', navigationSource);
    }

    if (navigationSource === 'sidebar') {
        sessionStorage.removeItem('navigationSource'); // Clean up
        if (debugAutoScroll) console.log('Fresh navigation: Sidebar navigation detected via sessionStorage');
        return true;
    }

    // Check if we came from sidebar navigation based on referrer
    try {
        const referrerUrl = new URL(document.referrer);
        const currentUrl = new URL(window.location.href);

        if (debugAutoScroll) {
            console.log('Referrer analysis:', {
                referrerPath: referrerUrl.pathname,
                currentPath: currentUrl.pathname,
                referrerSearch: referrerUrl.search,
                currentSearch: currentUrl.search
            });
        }

        // If referrer is from admin area but different page (likely sidebar navigation)
        if (referrerUrl.pathname.includes('/admin/') &&
            referrerUrl.pathname !== currentUrl.pathname) {

            // Additional check: if referrer is admin dashboard or other admin pages
            if (referrerUrl.pathname.includes('/admin/index.php') ||
                referrerUrl.pathname.includes('/admin/categories.php') ||
                referrerUrl.pathname.includes('/admin/orders.php') ||
                referrerUrl.pathname.includes('/admin/banners.php') ||
                referrerUrl.pathname.includes('/admin/users.php') ||
                referrerUrl.pathname.includes('/admin/settings.php') ||
                referrerUrl.pathname.endsWith('/admin/')) {
                if (debugAutoScroll) console.log('Fresh navigation: Admin page navigation detected via referrer');
                return true;
            }
        }

        // Special case: if we came from the same products.php page but with different or no parameters
        // This could happen when clicking sidebar "Sản phẩm" while already on products page with filters
        if (referrerUrl.pathname === currentUrl.pathname &&
            referrerUrl.pathname.includes('/admin/products.php')) {

            // Check if current page has no query parameters (clean products page)
            // and we have navigationSource marked as sidebar
            const currentParams = currentUrl.search;
            const referrerParams = referrerUrl.search;

            if (debugAutoScroll) {
                console.log('Same page navigation check:', {
                    currentParams,
                    referrerParams,
                    navigationSource: sessionStorage.getItem('navigationSource')
                });
            }

            // If current page has params but referrer doesn't (sidebar click from filtered page to clean page)
            // OR if current page has no params and navigation source is sidebar
            if ((!currentParams && sessionStorage.getItem('navigationSource') === 'sidebar') ||
                (currentParams && !referrerParams)) {
                if (debugAutoScroll) console.log('Fresh navigation: Sidebar click detected via parameter difference');
                return true;
            }
        }
    } catch (e) {
        // If URL parsing fails, assume it's fresh navigation
        if (debugAutoScroll) console.log('Fresh navigation: URL parsing failed', e);
        return true;
    }

    // Check for browser refresh (F5, Ctrl+R) using Performance API
    if (window.performance && window.performance.navigation) {
        // Check if page was reloaded
        if (window.performance.navigation.type === window.performance.navigation.TYPE_RELOAD) {
            if (debugAutoScroll) console.log('Fresh navigation: Browser reload detected');
            return true;
        }
    }

    // Modern browsers: Check using PerformanceNavigationTiming
    if (window.performance && window.performance.getEntriesByType) {
        const navigationEntries = window.performance.getEntriesByType('navigation');
        if (navigationEntries.length > 0) {
            const navEntry = navigationEntries[0];
            if (navEntry.type === 'reload') {
                if (debugAutoScroll) console.log('Fresh navigation: Modern reload detection');
                return true;
            }
        }
    }

    // Check for direct URL access (typed in address bar)
    if (window.performance && window.performance.navigation) {
        if (window.performance.navigation.type === window.performance.navigation.TYPE_NAVIGATE &&
            !document.referrer) {
            if (debugAutoScroll) console.log('Fresh navigation: Direct URL access');
            return true;
        }
    }

    // Additional check: if URL has no query parameters and no action flags, likely fresh navigation
    const urlParams = new URLSearchParams(window.location.search);
    const hasAnyParams = urlParams.toString().length > 0;
    const hasActionFlag = sessionStorage.getItem('autoScrollAfterAction') === 'true';

    if (!hasAnyParams && !hasActionFlag) {
        if (debugAutoScroll) console.log('Fresh navigation: No params and no action flag');
        return true;
    }

    // Final check: if we have URL parameters but they might be from previous session
    // and we don't have explicit action flag, check if this could be sidebar navigation
    if (hasAnyParams && !hasActionFlag) {
        // Check if we have common filter/pagination params that could be from previous session
        const hasFilterParams = urlParams.has('search') || urlParams.has('category_id') ||
                               urlParams.has('price_min') || urlParams.has('price_max') ||
                               urlParams.has('homepage_filter');
        const hasSortParams = urlParams.has('sort_by') || urlParams.has('sort_order');
        const hasPaginationParams = urlParams.has('page') || urlParams.has('items_per_page');

        // If we have these params but no explicit action flag and no form submission referrer,
        // it might be sidebar navigation with restored session parameters
        if ((hasFilterParams || hasSortParams || hasPaginationParams) &&
            !document.referrer.includes('products.php')) {
            if (debugAutoScroll) console.log('Fresh navigation: Likely sidebar with session params');
            return true;
        }

        // Additional check: if we only have pagination params (page/items_per_page)
        // and came from same products.php page, likely sidebar navigation
        if (hasPaginationParams && !hasFilterParams && !hasSortParams &&
            document.referrer.includes('/admin/products.php')) {

            // Parse referrer to check if it had different or no parameters
            try {
                const referrerUrl = new URL(document.referrer);
                const referrerParams = referrerUrl.search;
                const currentParams = window.location.search;

                if (debugAutoScroll) {
                    console.log('Pagination-only check:', {
                        referrerParams,
                        currentParams,
                        onlyPagination: hasPaginationParams && !hasFilterParams && !hasSortParams
                    });
                }

                // If referrer had no params but current has pagination params, likely sidebar click
                if (!referrerParams && currentParams) {
                    if (debugAutoScroll) console.log('Fresh navigation: Sidebar click with pagination params from session');
                    return true;
                }
            } catch (e) {
                if (debugAutoScroll) console.log('Error parsing referrer for pagination check:', e);
            }
        }
    }

    if (debugAutoScroll) console.log('Not fresh navigation - action-triggered navigation detected');
    return false;
}

function performSmartScroll() {
    const targetElement = document.querySelector('.table-card-header');
    if (!targetElement) return;

    // Force ALL elements to be immediately visible with no animations
    const allElements = document.querySelectorAll('.products-header, .products-stat-card, .filter-card, .modern-table-card');
    allElements.forEach(element => {
        element.classList.add('no-animation');
        element.classList.remove('animate-in');
        element.style.opacity = '1';
        element.style.transform = 'translateY(0)';
        element.style.transition = 'none';
        element.style.transitionDelay = '0s';
    });

    // Ensure table content is immediately visible before scrolling
    ensureTableContentVisible();

    // Force immediate repaint
    allElements.forEach(element => {
        element.offsetHeight; // Force reflow for each element
    });

    // Calculate offset to account for fixed headers/topbars
    const offset = calculateScrollOffset();

    // Get target position
    const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - offset;

    // Smooth scroll to target
    window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
    });

    // Add visual feedback
    addScrollFeedback(targetElement);
}

function calculateScrollOffset() {
    // Check for fixed headers or topbars
    const topbar = document.querySelector('.topbar');
    const navbar = document.querySelector('.navbar-fixed-top');
    const header = document.querySelector('header.fixed');
    const adminHeader = document.querySelector('.admin-header');

    let offset = 80; // Increased base offset for better positioning in admin interface

    // Specifically check for admin topbar
    if (topbar) {
        const topbarStyle = window.getComputedStyle(topbar);
        if (topbarStyle.position === 'fixed' || topbarStyle.position === 'sticky') {
            offset += topbar.offsetHeight;
        }
    }

    if (navbar && window.getComputedStyle(navbar).position === 'fixed') {
        offset += navbar.offsetHeight;
    }
    if (header && window.getComputedStyle(header).position === 'fixed') {
        offset += header.offsetHeight;
    }
    if (adminHeader && window.getComputedStyle(adminHeader).position === 'fixed') {
        offset += adminHeader.offsetHeight;
    }

    return offset;
}

function addScrollFeedback(element) {
    // Add a subtle highlight effect to show where we scrolled to
    element.style.transition = 'box-shadow 0.3s ease';
    element.style.boxShadow = '0 0 0 3px rgba(255, 107, 0, 0.3)';

    // Remove the highlight after a short delay
    setTimeout(() => {
        element.style.boxShadow = '';
        setTimeout(() => {
            element.style.transition = '';
        }, 300);
    }, 1500);
}

function cleanUpAutoScrollParam() {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('auto_scroll')) {
        urlParams.delete('auto_scroll');
        const newUrl = window.location.pathname + (urlParams.toString() ? '?' + urlParams.toString() : '');
        // Use replaceState to update URL without triggering page reload
        window.history.replaceState({}, '', newUrl);
    }
}

function setupAutoScrollTriggers() {
    const debugAutoScroll = false; // Match the debug setting

    // Track sidebar navigation to mark as fresh navigation (backup method)
    const sidebarProductLink = document.querySelector('.sidebar a[href*="products.php"]');
    if (sidebarProductLink) {
        if (debugAutoScroll) {
            console.log('Setting up backup sidebar tracking for products link');
        }
        sidebarProductLink.addEventListener('click', function() {
            sessionStorage.setItem('navigationSource', 'sidebar');
            if (debugAutoScroll) {
                console.log('Backup sidebar navigation marked for products');
            }
        });
    }

    // Filter form submission
    const filterForm = document.querySelector('form[action="products.php"]');
    if (filterForm) {
        filterForm.addEventListener('submit', function() {
            sessionStorage.setItem('autoScrollAfterAction', 'true');
            sessionStorage.removeItem('navigationSource'); // Clear navigation source
            if (debugAutoScroll) {
                console.log('Filter form submission - auto-scroll enabled');
            }
        });
    }

    // Sort links
    const sortLinks = document.querySelectorAll('.sort-link');
    sortLinks.forEach(link => {
        link.addEventListener('click', function() {
            sessionStorage.setItem('autoScrollAfterAction', 'true');
            sessionStorage.removeItem('navigationSource'); // Clear navigation source
            if (debugAutoScroll) {
                console.log('Sort link clicked - auto-scroll enabled');
            }
        });
    });

    // Pagination links
    const paginationLinks = document.querySelectorAll('.pagination .page-link');
    paginationLinks.forEach(link => {
        link.addEventListener('click', function() {
            sessionStorage.setItem('autoScrollAfterAction', 'true');
            sessionStorage.removeItem('navigationSource'); // Clear navigation source
            if (debugAutoScroll) {
                console.log('Pagination link clicked - auto-scroll enabled');
            }
        });
    });

    // Product action links (edit, delete, etc.)
    const actionLinks = document.querySelectorAll('a[href*="product-edit.php"], a[href*="product-delete.php"], a[href*="product_actions.php"]');
    actionLinks.forEach(link => {
        link.addEventListener('click', function() {
            sessionStorage.setItem('autoScrollAfterAction', 'true');
            sessionStorage.removeItem('navigationSource'); // Clear navigation source
            if (debugAutoScroll) {
                console.log('Product action link clicked - auto-scroll enabled');
            }
        });
    });

    // Reset filter button
    const resetFilterBtn = document.querySelector('a[href="products.php"]');
    if (resetFilterBtn && resetFilterBtn.textContent.includes('Đặt lại')) {
        resetFilterBtn.addEventListener('click', function() {
            sessionStorage.setItem('autoScrollAfterAction', 'true');
            sessionStorage.removeItem('navigationSource'); // Clear navigation source
            if (debugAutoScroll) {
                console.log('Reset filter clicked - auto-scroll enabled');
            }
        });
    }

    // Items per page form submission (already handled in separate script, but ensure consistency)
    const itemsPerPageForm = document.getElementById('itemsPerPageForm');
    if (itemsPerPageForm) {
        itemsPerPageForm.addEventListener('submit', function() {
            sessionStorage.setItem('autoScrollAfterAction', 'true');
            sessionStorage.removeItem('navigationSource'); // Clear navigation source
            if (debugAutoScroll) {
                console.log('Items per page form submission - auto-scroll enabled');
            }
        });
    }

    if (debugAutoScroll) {
        console.log('Auto-scroll triggers setup completed');
    }
}

// Simple CSS animation - no complex logic needed

// Enhanced mobile support
function isMobileDevice() {
    return window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

// Adjust scroll behavior for mobile
if (isMobileDevice()) {
    // Add mobile-specific optimizations
    document.addEventListener('DOMContentLoaded', function() {
        // Ensure smooth scrolling works on iOS
        document.documentElement.style.scrollBehavior = 'smooth';
    });
}

// Auto-submit filter form when select options change
document.addEventListener('DOMContentLoaded', function() {
    const filterForm = document.querySelector('form[action="products.php"]');
    const autoSubmitSelects = [
        'category_id',
        'homepage_filter',
        'price_type_filter'
    ];

    if (filterForm) {
        autoSubmitSelects.forEach(function(selectId) {
            const selectElement = document.getElementById(selectId);
            if (selectElement) {
                selectElement.addEventListener('change', function() {
                    // Submit form automatically when select value changes
                    filterForm.submit();
                });
            }
        });
    }
});
</script>
