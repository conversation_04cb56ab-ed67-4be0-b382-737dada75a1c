<?php
/**
 * Upload Image Handler
 * Xử lý upload hình ảnh cho trình soạn thảo đơn giản
 */

// Include init
require_once '../includes/init.php';

// Kiểm tra đăng nhập và quyền admin
if (!is_admin_logged_in() || !check_admin_role()) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Bạn không có quyền truy cập trang này.'
    ]);
    exit;
}

// Kiểm tra request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Phương thức không được hỗ trợ.'
    ]);
    exit;
}

// Kiểm tra file upload
if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Không có file được tải lên hoặc có lỗi xảy ra.'
    ]);
    exit;
}

// Thư mục lưu trữ hình ảnh
$upload_dir = '../uploads/editor/';

// Tạo thư mục nếu chưa tồn tại
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0755, true);
}

// Lấy thông tin file
$file_name = $_FILES['image']['name'];
$file_tmp = $_FILES['image']['tmp_name'];
$file_size = $_FILES['image']['size'];
$file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

// Kiểm tra định dạng file
$allowed_exts = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
if (!in_array($file_ext, $allowed_exts)) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Chỉ cho phép upload file hình ảnh (jpg, jpeg, png, gif, webp).'
    ]);
    exit;
}

// Kiểm tra kích thước file (giới hạn 5MB)
$max_size = 5 * 1024 * 1024; // 5MB
if ($file_size > $max_size) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Kích thước file không được vượt quá 5MB.'
    ]);
    exit;
}

// Tạo tên file mới để tránh trùng lặp
$new_file_name = 'editor_' . time() . '_' . uniqid() . '.' . $file_ext;
$upload_path = $upload_dir . $new_file_name;

// Upload file
if (move_uploaded_file($file_tmp, $upload_path)) {
    // Tạo URL cho hình ảnh
    $image_url = BASE_URL . '/uploads/editor/' . $new_file_name;
    
    // Trả về thông tin hình ảnh
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'url' => $image_url,
        'alt' => pathinfo($file_name, PATHINFO_FILENAME),
        'message' => 'Upload thành công!'
    ]);
} else {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Có lỗi xảy ra khi upload file.'
    ]);
}
