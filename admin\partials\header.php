<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME . ' Admin' : SITE_NAME . ' Admin'; ?></title>

    <!-- Favicon - Đ<PERSON>y đủ cho tất cả thiết bị và SEO -->
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo BASE_URL; ?>/assets/images/favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="96x96" href="<?php echo BASE_URL; ?>/assets/images/favicon/favicon-96x96.png">
    <link rel="icon" type="image/svg+xml" href="<?php echo BASE_URL; ?>/assets/images/favicon/favicon.svg">
    <link rel="icon" type="image/x-icon" href="<?php echo BASE_URL; ?>/assets/images/favicon/favicon.ico">
    <link rel="manifest" href="<?php echo BASE_URL; ?>/assets/images/favicon/site.webmanifest">
    <meta name="msapplication-TileColor" content="#f37321">
    <meta name="msapplication-TileImage" content="<?php echo BASE_URL; ?>/assets/images/favicon/web-app-manifest-192x192.png">
    <meta name="theme-color" content="#f37321">

    <!-- Custom fonts for this template-->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

    <!-- CustomEditor CSS -->
    <link href="<?php echo BASE_URL; ?>/admin/assets/css/custom-editor.css" rel="stylesheet">

    <!-- SimpleEditor CSS -->
    <link href="<?php echo BASE_URL; ?>/admin/assets/css/simple-editor.css" rel="stylesheet">

    <!-- Summernote CSS -->
    <link href="<?php echo BASE_URL; ?>/summernote-0.9.0-dist/summernote.min.css" rel="stylesheet">
    <link href="<?php echo BASE_URL; ?>/admin/assets/css/summernote-custom.css" rel="stylesheet">
    <link href="<?php echo BASE_URL; ?>/admin/assets/css/direct-summernote.css" rel="stylesheet">


    <!-- CKEditor 4 CSS (Giữ lại để tương thích với các trang khác) -->
    <style>
        /* Ẩn thông báo bảo mật của CKEditor */
        .cke_notification_warning {
            display: none !important;
        }

        /* Tùy chỉnh cho CKEditor 4 */
        .cke_chrome {
            border-radius: 4px;
            border-color: #ddd !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
        }

        .cke_top {
            border-bottom-color: #ddd !important;
            background: #f8f9fa !important;
            padding: 6px 8px !important;
        }

        .cke_bottom {
            border-top-color: #ddd !important;
            background: #f8f9fa !important;
        }

        /* Tùy chỉnh nội dung trong CKEditor */
        .cke_editable {
            padding: 10px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
        }

        .cke_editable h1, .cke_editable h2, .cke_editable h3,
        .cke_editable h4, .cke_editable h5, .cke_editable h6 {
            margin-top: 1em;
            margin-bottom: 0.5em;
            color: #333;
        }

        .cke_editable h2 {
            border-bottom: 1px solid #eee;
            padding-bottom: 0.3em;
        }

        .cke_editable p {
            margin-bottom: 1em;
        }

        .cke_editable table {
            width: 100%;
            border-collapse: collapse;
            margin: 1em 0;
        }

        .cke_editable table td,
        .cke_editable table th {
            border: 1px solid #ddd;
            padding: 8px;
        }

        .cke_editable ul,
        .cke_editable ol {
            padding-left: 2em;
            margin-bottom: 1em;
        }

        .cke_editable blockquote {
            border-left: 4px solid #ddd;
            padding-left: 1em;
            margin-left: 0;
            font-style: italic;
            color: #666;
        }

        /* Tùy chỉnh cho container của CKEditor */
        .product-content-editor {
            margin-bottom: 20px;
        }

        /* Tùy chỉnh cho các nút mẫu nội dung */
        .template-buttons {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .template-buttons .btn {
            margin-right: 5px;
            margin-bottom: 5px;
        }
    </style>

    <!-- Custom styles for this template-->
    <link href="<?php echo BASE_URL; ?>/admin/assets/css/admin.css" rel="stylesheet">
    <!-- Custom overrides -->
    <link href="<?php echo BASE_URL; ?>/admin/assets/css/custom.css" rel="stylesheet">
    <!-- Unified Notifications CSS -->
    <link href="<?php echo BASE_URL; ?>/admin/assets/css/unified-notifications.css" rel="stylesheet">
    <!-- Icon Alignment Fix CSS -->
    <link href="<?php echo BASE_URL; ?>/admin/assets/css/icon-alignment-fix.css" rel="stylesheet">

    <style>
        /* Cải thiện giao diện Select2 */
        .select2-container--default .select2-selection--single {
            height: calc(1.5em + 0.75rem + 2px);
            padding: 0.375rem 0.75rem;
            border: 1px solid #d1d3e2;
            border-radius: 0.35rem;
        }
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 1.5;
            padding-left: 0;
        }
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: calc(1.5em + 0.75rem + 2px);
        }

        /* Modern Sidebar Design - Enhanced */
        .sidebar {
            background: linear-gradient(180deg, #202834 0%, #1a222c 100%);
            box-shadow:
                4px 0 20px rgba(0, 0, 0, 0.15),
                0 0 40px rgba(0, 0, 0, 0.08);
            position: fixed;
            width: 280px;
            height: 100vh;
            z-index: 1000;
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            overflow-y: auto;
            overflow-x: hidden;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Enhanced Scrollbar */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
            border-radius: 3px;
            transition: all 0.3s ease;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.2) 100%);
        }

        /* Enhanced Brand Area - Clean Design */
        .sidebar-brand {
            display: flex !important;
            align-items: center;
            justify-content: center;
            padding: 1rem !important;
            /* background: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1); */
            transition: all 0.3s ease;
            height: 70px; /* Match topbar height for alignment */
        }

        .sidebar-brand:hover {
            background: rgba(255, 255, 255, 0.08);
        }

        .sidebar-brand img {
            width: 100%;
            height: auto;
            max-height: 45px;
            object-fit: contain;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
            transition: all 0.3s ease;
        }

        .sidebar-brand:hover img {
            transform: scale(1.05);
        }

        /* Enhanced Divider - Aligned with Topbar */
        .sidebar-divider.my-0 {
            border-top: 1px solid rgba(255, 255, 255, 0.15) !important;
            margin: 0 !important;
            position: relative;
            height: 0;
            padding-bottom:20px;
        }

        .sidebar-divider::after {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(243, 115, 33, 0.6), transparent);
        }

        .sidebar .nav-item {
            position: relative;
            margin-bottom: 0;
        }

        .sidebar .nav-item .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255, 255, 255, 0.85);
            font-weight: 500;
            font-size: 0.9rem;
            border-left: 3px solid transparent;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            position: relative;
            overflow: hidden;
            margin: 0.25rem 0.75rem;
            border-radius: 0.5rem;
        }

        .sidebar .nav-item .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
            opacity: 0;
            transition: all 0.3s ease;
            border-radius: inherit;
        }

        .sidebar .nav-item .nav-link:hover {
            color: #FFFFFF;
            background: linear-gradient(135deg, rgba(243, 115, 33, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
            border-left-color: #F37321;
            transform: translateX(4px);
        }

        .sidebar .nav-item .nav-link:hover::before {
            opacity: 1;
        }

        .sidebar .nav-item .nav-link i {
            margin-right: 1rem;
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.7);
            transition: all 0.3s ease;
            width: 20px;
            text-align: center;
            position: relative;
            z-index: 2;
            flex-shrink: 0;
        }

        /* Quy tắc cho hover state của nav-link thông thường */
        .sidebar .nav-item .nav-link:hover i {
            color: #F37321;
            transform: scale(1.1);
        }

        /* Quy tắc cho active state */
        .sidebar .nav-item.active .nav-link i {
            color: #FFFFFF !important;
            transform: scale(1.1);
        }

        /* Quy tắc cho hover state khi đang active */
        .sidebar .nav-item.active .nav-link:hover i {
            color: #FFFFFF !important;
            transform: scale(1.1);
        }

        /* Quy tắc cho expanded state của dropdown */
        .sidebar .nav-item .nav-link[aria-expanded="true"] i {
            color: #FFFFFF !important;
            transform: scale(1.1);
        }

        /* Quy tắc cho hover state khi đang expanded */
        .sidebar .nav-item .nav-link[aria-expanded="true"]:hover i {
            color: #FFFFFF !important;
            transform: scale(1.1);
        }

        .sidebar .nav-item .nav-link span {
            white-space: nowrap;    /* Ngăn text xuống dòng */
            overflow: hidden;         /* Ẩn phần text bị tràn */
            text-overflow: ellipsis;  /* Hiển thị dấu ... cho text bị tràn */
            flex-grow: 1;           /* Cho phép span chiếm không gian còn lại */
            min-width: 0;           /* Cần thiết cho text-overflow ellipsis trong flex item */
        }

        .sidebar .nav-item.active .nav-link {
            color: #FFFFFF;
            background: linear-gradient(135deg, #F37321 0%, #e6651c 100%);
            border-left-color: #F37321;
            font-weight: 600;
            transform: translateX(4px);
            box-shadow:
                0 4px 12px rgba(243, 115, 33, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .sidebar .nav-item.active .nav-link::before {
            opacity: 1;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        }

        .sidebar .nav-item .collapse-inner {
            background: #1a1f2c; /* Màu nền tối hơn để tạo độ tương phản */
            border-radius: 0.5rem;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.15);
            margin: 0.25rem 1rem;
            padding: 0.5rem;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .sidebar .nav-item .collapse-header {
            padding: 0.5rem 1rem;
            margin-bottom: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.7); /* Màu chữ header hơi nhạt để phân biệt */
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .sidebar .nav-item .collapse-item {
            padding: 0.75rem 1rem;
            margin: 0.25rem 0.5rem;
            display: flex;
            align-items: center;
            color: #FFFFFF;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.2s ease;
            font-size: 0.875rem;
            font-weight: 400;
            background: rgba(255, 255, 255, 0.03);
            border-left: 2px solid transparent;
        }

        .sidebar .nav-item .collapse-item:hover {
            background: linear-gradient(145deg, rgba(243, 115, 33, 0.57), rgba(243, 115, 33, 0.1));
            color: #FFFFFF;
            border-left-color: #F37321;
            transform: translateX(3px);
        }

        .sidebar .nav-item .collapse-item.active {
            background: linear-gradient(145deg, rgba(243, 115, 33, 0.57), rgba(243, 115, 33, 0.1));
            color: #FFFFFF;
            font-weight: 500;
            border-left-color: #F37321;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .sidebar .nav-item .collapse-item:active {
            transform: translateY(1px); /* Hiệu ứng nhấn xuống nhẹ */
        }

        .sidebar .nav-item .nav-link[aria-expanded="true"] {
            color: #FFFFFF;
            background-color: rgba(243, 115, 33, 0.15); /* Đồng bộ với màu active của items */
            font-weight: 500;
            border-left-color: #F37321;
        }

        .sidebar .nav-item .nav-link[data-toggle="collapse"]::after {
            margin-left: auto;
            padding-left: 0.75rem;
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            content: '\f107';
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.875rem;
        }

        .sidebar .nav-item .nav-link[aria-expanded="true"]::after {
            color: #FFFFFF !important; /* Đặt màu icon mũi tên thành trắng khi dropdown mở */
            transform: rotate(180deg); /* Xoay mũi tên lên trên */
        }

        .sidebar .nav-item .nav-link[data-toggle="collapse"].collapsed::after {
            content: '\f105';
            transform: rotate(0deg);
        }

        /* Sửa lỗi cho content wrapper */
        #content-wrapper {
            margin-left: 280px; /* Đồng bộ với chiều rộng sidebar mới */
            width: calc(100% - 280px); /* Đồng bộ với chiều rộng sidebar mới */
            min-height: 100vh;
            background-color: #f8f9fc;
            transition: all 0.3s ease;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 0;
                overflow: hidden;
            }

            #content-wrapper {
                margin-left: 0;
                width: 100%;
            }

            .sidebar.toggled {
                width: 280px; /* Đồng bộ với chiều rộng sidebar mới khi toggled */
            }
        }

        /* Custom Scrollbar Styling for Sidebar */
        /* For Webkit-based browsers (Chrome, Safari, Edge) */
        .sidebar::-webkit-scrollbar {
            width: 8px; /* Độ rộng của thanh cuộn */
        }

        .sidebar::-webkit-scrollbar-track {
            background: #f8f9fc; /* Màu nền của track - gần với màu nền content */
            border-radius: 10px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background-color: #d1d3e2; /* Màu của con trượt (thumb) - màu xám nhạt */
            border-radius: 10px;
            border: 2px solid #f8f9fc; /* Tạo khoảng cách giữa thumb và track */
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background-color: #b7b9cc; /* Màu của thumb khi hover - đậm hơn một chút */
        }

        /* For Firefox */
        .sidebar {
            scrollbar-width: thin; /* "thin" hoặc "auto" hoặc "none" */
            scrollbar-color: #d1d3e2 #f8f9fc; /* thumb_color track_color */
        }



        /* Topbar cải tiến */
        .topbar {
            background-color: #202834 !important; /* Đồng bộ màu nền với sidebar */
            box-shadow: none !important; /* Loại bỏ shadow để đảm bảo màu đều */
        }

        .topbar .navbar-nav .nav-item .nav-link {
            color: #495057; /* Đậm hơn một chút so với #5a5c69 */
            font-weight: 500; /* Giữ nguyên hoặc tăng lên 600 nếu muốn */
            padding: 0 1rem;
            height: 4.375rem;
            display: flex;
            align-items: center;
            font-size: 0.9rem; /* Tăng nhẹ kích thước font chung cho topbar links */
        }

        .topbar .navbar-nav .nav-item .nav-link:hover {
            color: #4e73df;
        }

        .topbar .navbar-nav .nav-item .nav-link i {
            font-size: 0.9rem; /* Giảm nhẹ icon để cân đối với text */
            margin-right: 0.5rem;
        }

        /* Cụ thể cho tên người dùng */
        .topbar .navbar-nav .nav-item .dropdown-toggle .text-gray-600 {
            color: #495057 !important; /* Ghi đè màu chữ cho tên người dùng, !important nếu cần */
            font-size: 0.875rem; /* Đồng bộ với các link khác hoặc điều chỉnh */
            /* font-weight: 600; */ /* Có thể tăng độ đậm nếu muốn */
        }

        /* CSS mới cho bố cục User Info Topbar */
        .topbar .nav-item .dropdown-toggle {
            padding-right: 0.75rem; /* Giảm padding phải để icon dropdown gần hơn nếu cần */
        }

        .topbar .user-avatar-lg {
            width: 40px;  /* Kích thước avatar lớn hơn */
            height: 40px;
            margin-right: 0.5rem; /* Giảm khoảng cách với text */
        }

        .topbar .user-info-text {
            display: flex;
            flex-direction: column;
            align-items: flex-start; /* Căn text sang trái */
            line-height: 1.2;
        }

        .topbar .user-info-text .user-name {
            font-weight: bold;
            font-size: 0.9rem; /* Kích thước tên */
            color: #FFFFFF; /* Chuyển thành màu trắng */
        }

        .topbar .user-info-text .user-role {
            font-size: 0.75rem; /* Kích thước chức vụ */
            color: rgba(255, 255, 255, 0.7);   /* Màu trắng mờ cho chức vụ */
        }

        .topbar .nav-item .dropdown-toggle::after {
            display: none !important; /* Ẩn icon dropdown mặc định của Bootstrap */
        }

        /* CSS cho icon dropdown tùy chỉnh */
        .topbar .custom-dropdown-icon {
            margin-left: 0.5rem;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.8rem;
            display: inline-block; /* Đảm bảo căn chỉnh và transform hoạt động tốt */
            vertical-align: middle; /* Căn chỉnh tốt hơn với text nếu có */
        }
        .topbar .custom-dropdown-icon i {
            transition: transform 0.25s ease-out;
            display: inline-block; /* Cho phép transform */
        }
        .topbar .custom-dropdown-icon i.rotated-up {
            transform: rotate(180deg);
        }

        /* CSS cho mục "Xem website" nổi bật hơn */
        .topbar .topbar-view-website-link {
            font-weight: 600; /* Tăng độ đậm */
            color: #FFFFFF !important; /* Chuyển thành màu trắng */
            font-size: 0.9rem; /* Giữ nguyên hoặc điều chỉnh nếu cần */
        }

        .topbar .topbar-view-website-link i {
            font-size: 1.15rem; /* Tăng nhẹ kích thước icon */
            color: #FFFFFF; /* Chuyển thành màu trắng */
            margin-right: 0.4rem; /* Điều chỉnh khoảng cách icon */
        }

        .topbar .topbar-view-website-link:hover {
            color: #f8f9fa; /* Màu trắng hơi xám khi hover */
            background-color: rgba(255, 255, 255, 0.05); /* Nền nhẹ khi hover */
        }

        .topbar .topbar-view-website-link:hover i {
            color: #f8f9fa; /* Đồng bộ màu icon khi hover */
        }

        /* Redesign Dropdown User Information */
        .topbar .nav-item .dropdown-menu {
            background-color: #202834;
            border: 1px solid #2b3444;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.35);
            border-radius: 0.4rem;
            padding-top: 0;
            padding-bottom: 0;
            margin-top: 8px;
            animation: none !important; /* Tắt animation mặc định nếu có */
            display: block !important; /* Ghi đè display:none của Bootstrap để transition hoạt động */
            opacity: 0;
            transform: translateY(-10px); /* Trượt lên trên khi ẩn */
            visibility: hidden;
            pointer-events: none;
            /* Delay transition của visibility khi ẩn */
            transition: opacity 0.25s ease-out, transform 0.25s ease-out, visibility 0s linear 0.25s;
        }

        .topbar .nav-item .dropdown-menu.show {
            opacity: 1;
            transform: translateY(0); /* Trượt về vị trí ban đầu khi hiện */
            visibility: visible;
            pointer-events: auto;
            /* Reset delay transition của visibility khi hiện */
            transition-delay: 0s, 0s, 0s;
        }

        .topbar .user-dropdown-header {
            padding: 0.8rem 1.25rem;
            border-bottom: 1px solid #2b3444;
        }

        .topbar .user-dropdown-header-title {
            color: rgba(255, 255, 255, 0.8) !important; /* Đồng bộ màu và tăng độ ưu tiên */
            font-weight: 600;
            font-size: 0.95rem;
        }

        .topbar .user-dropdown-header-email {
            color: rgba(255, 255, 255, 0.65);
            font-size: 0.75rem;
        }

        .topbar .dropdown-item {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.7rem 1.25rem;
            font-size: 0.875rem;
            transition: background-color 0.15s ease, color 0.15s ease;
        }

        .topbar .dropdown-item i {
            color: rgba(255, 255, 255, 0.55);
            margin-right: 0.75rem;
            width: 16px; /* Đảm bảo icon thẳng hàng */
            text-align: center;
            transition: color 0.15s ease;
        }

        .topbar .dropdown-item:hover,
        .topbar .dropdown-item:focus {
            background-color: #2b3444;
            color: #FFFFFF;
        }

        .topbar .dropdown-item:hover i,
        .topbar .dropdown-item:focus i {
            color: rgba(255, 255, 255, 0.75);
        }

        .topbar .dropdown-item:active {
            background-color: #343a40; /* Màu nền đậm hơn khi click */
            color: #FFFFFF;
        }

        .topbar .dropdown-divider {
            border-top: 1px solid #2b3444;
            margin: 0; /* Loại bỏ margin mặc định */
        }
        /* End of Redesign Dropdown User Information */

        /* Style cho menu Blog */
        .sidebar .nav-item .nav-link[data-toggle="collapse"] {
            position: relative;
            transition: all 0.2s ease-in-out;
        }

        /* Icon mũi tên của menu Blog */
        .sidebar .nav-item .nav-link[data-toggle="collapse"]::after {
            margin-left: auto;
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            content: '\f107';
            transition: all 0.2s ease;
            color: rgba(255, 255, 255, 0.6);
        }

        /* Khi menu Blog được mở */
        .sidebar .nav-item .nav-link[aria-expanded="true"] {
            background-color: #F37321; /* Thay đổi thành màu cam active */
            color: #FFFFFF;
            font-weight: 600; /* Thêm font-weight active */
            border-left: 4px solid #F37321; /* Đồng bộ border active */
            border-radius: 8px;
            margin-bottom: 4px;
        }

        .sidebar .nav-item .nav-link[aria-expanded="true"]::after {
            transform: rotate(180deg);
            color: #FFFFFF !important; /* Đặt màu icon mũi tên thành trắng khi dropdown mở */
        }

        /* Container của dropdown Blog */
        .sidebar .nav-item .collapse-inner {
            background: #2b3444;
            margin: 0.25rem 0.75rem; /* Giảm margin để khớp với chiều rộng của nav-link */
            padding: 0.75rem 0.75rem; /* Thêm padding hai bên để căn chỉnh nội dung */
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            border: none;
        }

        /* Items trong dropdown Blog */
        .sidebar .nav-item .collapse-item {
            padding: 0.75rem 0.75rem; /* Điều chỉnh padding của items để khớp với container */
            margin: 0.25rem 0; /* Chỉ để margin trên dưới */
            display: flex;
            align-items: center;
            color: #FFFFFF;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.2s ease;
            font-size: 0.875rem;
            font-weight: 400;
            background: rgba(255, 255, 255, 0.03);
            border-left: 2px solid transparent;
        }

        /* Hover effect cho items */
        .sidebar .nav-item .collapse-item:hover {
            background: linear-gradient(145deg, rgba(243, 115, 33, 0.57), rgba(243, 115, 33, 0.1));
            color: #F37321 !important;
            border-left-color: #F37321;
            transform: translateX(3px);
        }

        /* Style cho item active */
        .sidebar .nav-item .collapse-item.active {
            background: linear-gradient(145deg, rgba(243, 115, 33, 0.57), rgba(243, 115, 33, 0.1));
            color: #FFFFFF !important;
            font-weight: 500;
            border-left-color: #F37321;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        /* Active item khi hover - giữ màu trắng */
        .sidebar .nav-item .collapse-item.active:hover {
            color: #FFFFFF !important;
        }

        /* Đảm bảo màu chữ cho các mục con trong dropdown Blog */
        .sidebar .nav-item .collapse .collapse-inner .collapse-item,
        .sidebar .nav-item .collapsing .collapse-inner .collapse-item {
            color: #e3e6f0;
        }

        /* Override hover color for collapse items */
        .sidebar .nav-item .collapse .collapse-inner .collapse-item:hover,
        .sidebar .nav-item .collapsing .collapse-inner .collapse-item:hover {
            color: #F37321 !important;
        }

        /* Header của dropdown */
        .sidebar .nav-item .collapse-header {
            padding: 0.5rem 1.25rem;
            margin-bottom: 0.5rem;
            font-size: 0.75rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.7);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Divider trong dropdown */
        .sidebar .nav-item .collapse-inner hr {
            margin: 0.75rem 0.5rem;
            border-color: rgba(255, 255, 255, 0.05);
        }

        /* Animation cho dropdown sidebar - Giống topbar dropdown */
        .sidebar .collapse {
            overflow: visible; /* Cho phép nội dung tràn ra nếu cần trong quá trình transform */
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px); /* Trượt lên trên khi ẩn */
            transition: opacity 0.25s ease-out, transform 0.25s ease-out, visibility 0s linear 0.25s;
            will-change: opacity, transform;
            display: none; /* Sẽ được ghi đè bởi .show */
        }

        .sidebar .collapse.show {
            display: block; /* Bootstrap thường dùng display: block cho .show */
            opacity: 1;
            visibility: visible;
            transform: translateY(0); /* Trượt về vị trí ban đầu khi hiện */
            transition-delay: 0s, 0s, 0s;
        }

        /* Ghi đè .collapsing để nó không can thiệp */
        .sidebar .collapsing {
            height: 0 !important; /* Giữ height 0 trong quá trình chuyển đổi của Bootstrap */
            overflow: hidden !important;
            opacity: 0; /* Giữ ẩn trong lúc Bootstrap tính toán */
            display: block !important; /* Vẫn cho phép Bootstrap tính toán */
            transition: none !important; /* Tắt transition của Bootstrap cho .collapsing */
        }

        /* Loại bỏ keyframes nếu không cần thiết nữa */
        /* @keyframes fadeInDown { ... } */

        /* Thêm icon cho các menu items */
        .sidebar .nav-item .collapse-item::before {
            content: '';
            width: 6px;
            height: 6px;
            background: currentColor;
            border-radius: 50%;
            margin-right: 12px;
            opacity: 0.5;
            transition: all 0.2s ease;
        }

        .sidebar .nav-item .collapse-item:hover::before,
        .sidebar .nav-item .collapse-item.active::before {
            opacity: 1;
            background: #F37321;
        }



        /* Enhanced Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                box-shadow: none;
            }

            .sidebar.toggled {
                transform: translateX(0);
                box-shadow:
                    4px 0 20px rgba(0, 0, 0, 0.25),
                    0 0 40px rgba(0, 0, 0, 0.15);
            }
        }

        .sidebar .nav-item.active .nav-link i {
            color: #FFFFFF; /* Đặt màu icon thành trắng khi tab active */
        }

        /* Đảm bảo icon tab Blog màu trắng khi active và hover */
        .sidebar .nav-item.active > .nav-link:hover i {
            color: #FFFFFF !important; /* Giữ màu icon trắng khi active và hover với độ ưu tiên cao hơn */
        }

        /* Modern Blog Dropdown Design - Beautiful and Consistent */
        .sidebar .nav-item .collapse-inner {
            background: linear-gradient(135deg, #2b3444 0%, #1e2a32 100%);
            margin: 0.5rem 0; /* Loại bỏ margin left/right để bằng với tab */
            padding: 1rem 0; /* Tăng padding cho đẹp hơn */
            border-radius: 12px; /* Border radius hiện đại hơn */
            box-shadow:
                0 8px 25px rgba(0, 0, 0, 0.15),
                0 4px 10px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1); /* Highlight nhẹ */
            border: 1px solid rgba(255, 255, 255, 0.1); /* Border subtle */
            position: relative;
            overflow: hidden;
            width: 100%; /* Đảm bảo chiều rộng bằng với tab */
        }

        /* Hiệu ứng gradient overlay */
        .sidebar .nav-item .collapse-inner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #F37321 0%, #ff8f00 50%, #F37321 100%);
            border-radius: 12px 12px 0 0;
        }

        /* Hiệu ứng subtle pattern */
        .sidebar .nav-item .collapse-inner::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, rgba(243, 115, 33, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            border-radius: 12px;
        }

        /* Collapse items styling */
        .sidebar .nav-item .collapse-inner .collapse-item {
            position: relative;
            z-index: 2;
            color: #e3e6f0;
            padding: 0.75rem 1.5rem;
            margin: 0.25rem 1rem;
            border-radius: 8px;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            font-weight: 500;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            text-decoration: none;
            border: 1px solid transparent;
        }

        /* Hover effects */
        .sidebar .nav-item .collapse-inner .collapse-item:hover {
            background: linear-gradient(135deg, rgba(243, 115, 33, 0.2) 0%, rgba(243, 115, 33, 0.1) 100%);
            color: #F37321 !important;
            transform: translateX(4px);
            border-color: rgba(243, 115, 33, 0.3);
            box-shadow: 0 4px 12px rgba(243, 115, 33, 0.2);
        }

        /* Active state */
        .sidebar .nav-item .collapse-inner .collapse-item.active {
            background: linear-gradient(135deg, #F37321 0%, #e06518 100%);
            color: #ffffff !important;
            font-weight: 600;
            transform: translateX(6px);
            box-shadow:
                0 6px 15px rgba(243, 115, 33, 0.3),
                0 2px 6px rgba(243, 115, 33, 0.2);
            border-color: rgba(255, 255, 255, 0.2);
        }

        /* Active state khi hover - giữ màu trắng */
        .sidebar .nav-item .collapse-inner .collapse-item.active:hover {
            color: #ffffff !important;
        }

        /* Icon styling for collapse items */
        .sidebar .nav-item .collapse-inner .collapse-item::before {
            content: '▸';
            margin-right: 0.75rem;
            font-size: 0.75rem;
            color: #F37321;
            transition: all 0.3s ease;
        }

        .sidebar .nav-item .collapse-inner .collapse-item:hover::before {
            color: #F37321;
            transform: translateX(2px);
        }

        .sidebar .nav-item .collapse-inner .collapse-item.active::before {
            content: '▶';
            color: #ffffff;
            transform: translateX(2px);
        }
    </style>
</head>
<body id="page-top">
    <!-- Page Wrapper -->
    <div id="wrapper">
        <!-- Sidebar -->
        <ul class="navbar-nav sidebar" id="accordionSidebar">
            <!-- Sidebar - Brand (Logo) -->
            <a class="sidebar-brand" href="<?php echo BASE_URL; ?>/admin/">
                <img src="<?php echo BASE_URL; ?>/assets/images/logo-admin/logo-chu-trang.svg" alt="<?php echo defined('SITE_NAME') ? SITE_NAME . ' Logo' : 'Logo'; ?>">
            </a>

            <!-- Divider (This one remains and aligns with topbar bottom) -->
            <hr class="sidebar-divider my-0">

            <?php
            // Get the current script name to determine active states
            $current_script_name = basename($_SERVER['SCRIPT_FILENAME']);
            ?>

            <?php // Removed the extra <hr class="sidebar-divider"> that was here ?>

            <!-- Nav Item - Dashboard (Tổng quan) -->
            <li class="nav-item<?php if ($current_script_name == 'index.php') echo ' active'; ?>">
                <a class="nav-link" href="<?php echo BASE_URL; ?>/admin">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Tổng quan</span>
                </a>
            </li>

            <!-- Nav Item - Products -->
            <li class="nav-item<?php if ($current_script_name == 'products.php' || $current_script_name == 'product-add.php' || $current_script_name == 'product-edit.php') echo ' active'; ?>">
                <a class="nav-link" href="<?php echo BASE_URL; ?>/admin/products.php">
                    <i class="fas fa-fw fa-box"></i>
                    <span>Sản phẩm</span>
                </a>
            </li>

            <!-- Nav Item - Categories -->
            <li class="nav-item<?php if ($current_script_name == 'categories.php' || $current_script_name == 'category-add.php' || $current_script_name == 'category-edit.php') echo ' active'; ?>">
                <a class="nav-link" href="<?php echo BASE_URL; ?>/admin/categories.php">
                    <i class="fas fa-fw fa-folder"></i>
                    <span>Danh mục</span>
                </a>
            </li>

            <!-- Nav Item - Orders -->
            <li class="nav-item<?php if ($current_script_name == 'orders.php' || $current_script_name == 'order-detail.php') echo ' active'; ?>">
                <a class="nav-link" href="<?php echo BASE_URL; ?>/admin/orders.php">
                    <i class="fas fa-fw fa-shopping-cart"></i>
                    <span>Đơn hàng</span>
                </a>
            </li>

            <!-- Nav Item - Banners -->
            <li class="nav-item<?php if ($current_script_name == 'banners.php' || $current_script_name == 'banner-add.php' || $current_script_name == 'banner-edit.php') echo ' active'; ?>">
                <a class="nav-link" href="<?php echo BASE_URL; ?>/admin/banners.php">
                    <i class="fas fa-fw fa-images"></i>
                    <span>Quản lý banner</span>
                </a>
            </li>

            <!-- Nav Item - Users -->
            <li class="nav-item<?php if ($current_script_name == 'users.php' || $current_script_name == 'user-detail.php') echo ' active'; ?>">
                <a class="nav-link" href="<?php echo BASE_URL; ?>/admin/users.php">
                    <i class="fas fa-fw fa-users"></i>
                    <span>Người dùng</span>
                </a>
            </li>

            <!-- Nav Item - Reviews -->
            <li class="nav-item<?php if ($current_script_name == 'reviews.php') echo ' active'; ?>">
                <a class="nav-link" href="<?php echo BASE_URL; ?>/admin/reviews.php">
                    <i class="fas fa-fw fa-comments"></i>
                    <span>Đánh giá & Bình luận</span>
                    <?php
                    // Lấy số lượng đánh giá và phản hồi chờ duyệt (đoạn này giữ nguyên, không liên quan active state)
                    // global $pdo; // Giả sử $pdo đã được khởi tạo và có sẵn
                    // if (function_exists('get_pending_reviews') && function_exists('get_pending_replies')) {
                    //    $pending_reviews = get_pending_reviews();
                    //    $pending_replies = get_pending_replies();
                    //    $total_pending = count($pending_reviews['reviews']) + count($pending_replies['replies']);
                    //    if ($total_pending > 0):
                    //    echo '<span class="badge badge-danger badge-counter ml-1">' . $total_pending . '</span>';
                    //    endif;
                    // }
                    ?>
                </a>
            </li>

            <!-- Nav Item - Testimonials -->
            <li class="nav-item<?php if ($current_script_name == 'testimonials.php' || $current_script_name == 'add-testimonial.php' || $current_script_name == 'edit-testimonial.php') echo ' active'; ?>">
                <a class="nav-link" href="<?php echo BASE_URL; ?>/admin/testimonials.php">
                    <i class="fas fa-fw fa-quote-left"></i>
                    <span>Cảm nhận khách hàng</span>
                </a>
            </li>

            <?php
            // Define an array of blog-related script names (excluding blog-post-products.php)
            $blog_pages = [
                'blog-posts.php',
                'blog-post-edit.php',
                'blog-categories.php',
                'blog-category-edit.php',
                'blog-category-add.php',
                'blog-tags.php',
                'blog-tag-edit.php',
                'blog-tag-add.php',
                'blog-authors.php',
                'blog-author-edit.php',
                'blog-author-add.php',
                'blog-author-delete.php',
                'blog-comments.php'
            ];

            $is_blog_page_context = in_array($current_script_name, $blog_pages);
            $blog_main_link_classes = 'nav-link';
            if (!$is_blog_page_context) {
                $blog_main_link_classes .= ' collapsed';
            }
            $blog_main_link_aria_expanded = $is_blog_page_context ? 'true' : 'false';
            $blog_collapse_div_classes = 'collapse';
            if ($is_blog_page_context) {
                $blog_collapse_div_classes .= ' show';
            }
            ?>
            <!-- Nav Item - Blog -->
            <li class="nav-item<?php if ($is_blog_page_context) echo ' active'; ?>">
                <a class="<?php echo $blog_main_link_classes; ?>" href="#" data-toggle="collapse" data-target="#collapseBlog" aria-expanded="<?php echo $blog_main_link_aria_expanded; ?>" aria-controls="collapseBlog">
                    <i class="fas fa-fw fa-newspaper"></i>
                    <span>Blog</span>
                </a>
                <div id="collapseBlog" class="<?php echo $blog_collapse_div_classes; ?>" aria-labelledby="headingBlog">
                    <div class="collapse-inner">
                        <a class="collapse-item<?php if ($current_script_name == 'blog-posts.php' || $current_script_name == 'blog-post-edit.php') echo ' active'; ?>" href="<?php echo BASE_URL; ?>/admin/blog-posts.php">Bài viết</a>
                        <a class="collapse-item<?php if ($current_script_name == 'blog-categories.php' || $current_script_name == 'blog-category-add.php' || $current_script_name == 'blog-category-edit.php') echo ' active'; ?>" href="<?php echo BASE_URL; ?>/admin/blog-categories.php">Danh mục</a>
                        <a class="collapse-item<?php if ($current_script_name == 'blog-tags.php' || $current_script_name == 'blog-tag-add.php' || $current_script_name == 'blog-tag-edit.php') echo ' active'; ?>" href="<?php echo BASE_URL; ?>/admin/blog-tags.php">Tags</a>
                        <a class="collapse-item<?php if ($current_script_name == 'blog-authors.php' || $current_script_name == 'blog-author-add.php' || $current_script_name == 'blog-author-edit.php' || $current_script_name == 'blog-author-delete.php') echo ' active'; ?>" href="<?php echo BASE_URL; ?>/admin/blog-authors.php">Tác giả</a>
                        <a class="collapse-item<?php if ($current_script_name == 'blog-comments.php') echo ' active'; ?>" href="<?php echo BASE_URL; ?>/admin/blog-comments.php">Bình luận</a>
                    </div>
                </div>
            </li>

            <!-- Nav Item - Footer Settings -->
            <li class="nav-item<?php if ($current_script_name == 'footer-settings.php' || $current_script_name == 'update-footer-settings.php') echo ' active'; ?>">
                <a class="nav-link" href="<?php echo BASE_URL; ?>/admin/footer-settings.php">
                    <i class="fas fa-fw fa-shoe-prints"></i>
                    <span>Quản lý Footer</span>
                </a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">
        </ul>
        <!-- End of Sidebar -->

        <script>
        document.addEventListener('DOMContentLoaded', function () {
            const sidebar = document.getElementById('accordionSidebar');

            if (sidebar) {
                $(sidebar).on('shown.bs.collapse', function (e) {
                    const openedCollapseTarget = e.target;
                    const navLinkToggler = sidebar.querySelector('[data-target="#' + openedCollapseTarget.id + '"]');

                    if (navLinkToggler) {
                        const parentNavItemOfToggler = navLinkToggler.closest('.nav-item');
                        const allNavItems = sidebar.querySelectorAll('.nav-item');

                        allNavItems.forEach(navItem => {
                            // Chỉ loại bỏ 'active' nếu navItem không phải là mục cha của collapse vừa mở
                            if (navItem !== parentNavItemOfToggler) {
                                navItem.classList.remove('active');
                            }
                        });
                        // CSS sẽ tự xử lý việc active cho parentNavItemOfToggler dựa trên [aria-expanded="true"]
                    }
                });
            }

            // --- Xử lý icon dropdown user info bằng JS thuần, không phụ thuộc jQuery/Bootstrap event ---
            var toggle = document.querySelector('#userDropdown');
            console.log('User Dropdown Toggle Element:', toggle);

            if (toggle) {
                var iconSpan = toggle.querySelector('.custom-dropdown-icon');
                if (!iconSpan) {
                    iconSpan = document.createElement('span');
                    iconSpan.className = 'custom-dropdown-icon';
                    iconSpan.innerHTML = '<i class="fas fa-chevron-down"></i>';
                    toggle.appendChild(iconSpan);
                    console.log('Custom icon appended.');
                }
                var iconEl = iconSpan.querySelector('i');
                console.log('Icon Element:', iconEl);

                var dropdownMenu = toggle.nextElementSibling;
                if (dropdownMenu && dropdownMenu.classList.contains('dropdown-menu')) {
                    console.log('Dropdown Menu Element found:', dropdownMenu);

                    var observer = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.attributeName === 'class') {
                                var isShown = dropdownMenu.classList.contains('show');
                                console.log('Dropdown menu class changed, isShown:', isShown);
                                if (isShown) {
                                    iconEl.className = 'fas fa-chevron-down rotated-up';
                                    console.log('Icon class set to: fas fa-chevron-down rotated-up');
                                } else {
                                    iconEl.className = 'fas fa-chevron-down';
                                    console.log('Icon class set to: fas fa-chevron-down');
                                }
                            }
                        });
                    });
                    observer.observe(dropdownMenu, { attributes: true, attributeFilter: ['class'] });
                    console.log('MutationObserver is observing dropdownMenu for class changes.');
                } else {
                    console.error('Dropdown Menu Element NOT FOUND next to #userDropdown or does not have .dropdown-menu class. Icon change might not work.');
                }
            } else {
                console.log('User Dropdown Toggle Element NOT FOUND with #userDropdown');
            }
        });
        </script>

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <!-- Main Content -->
            <div id="content">
                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
                    <!-- Sidebar Toggle (Topbar) -->
                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">
                        <i class="fa fa-bars"></i>
                    </button>

                    <!-- Topbar Navbar -->
                    <ul class="navbar-nav ml-auto">
                        <!-- Nav Item - View Website -->
                        <li class="nav-item">
                            <a class="nav-link topbar-view-website-link" href="<?php echo BASE_URL; ?>" target="_blank">
                                <i class="fas fa-globe fa-fw"></i> Xem website
                            </a>
                        </li>

                        <div class="topbar-divider d-none d-sm-block"></div>

                        <!-- Nav Item - User Information -->
                        <li class="nav-item dropdown no-arrow">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <?php
                                $avatar_src = BASE_URL . '/admin/assets/img/undraw_profile.svg'; // Default avatar
                                if (function_exists('get_user_by_id') && isset($_SESSION['user_id'])) {
                                    $admin_user = get_user_by_id($_SESSION['user_id']);
                                    if (!empty($admin_user['avatar'])) {
                                        $avatar_src = BASE_URL . '/uploads/avatars/' . $admin_user['avatar'];
                                    }
                                }
                                ?>
                                <img class="img-profile rounded-circle user-avatar-lg" src="<?php echo $avatar_src; ?>" alt="Avatar">

                                <div class="user-info-text">
                                    <span class="user-name"><?php echo isset($_SESSION['full_name']) ? $_SESSION['full_name'] : 'Admin'; ?></span>
                                    <span class="user-role">Quản trị viên</span>
                                </div>
                            </a>
                            <!-- Dropdown - User Information -->
                            <div class="dropdown-menu dropdown-menu-right shadow animated--grow-in" aria-labelledby="userDropdown">
                                <?php
                                // Đảm bảo $admin_user được định nghĩa và có thông tin cần thiết
                                // Biến $admin_user đã được lấy ở phần hiển thị avatar ngay phía trên
                                if (isset($admin_user) && is_array($admin_user)):
                                ?>
                                <div class="user-dropdown-header px-3 py-2">
                                    <div class="user-dropdown-header-title font-weight-bold text-dark"><?php echo htmlspecialchars($admin_user['full_name']); ?></div>
                                    <div class="user-dropdown-header-email text-muted small"><?php echo htmlspecialchars($admin_user['email']); ?></div>
                                </div>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/profile.php"> <?php // Link đến trang profile admin ?>
                                    <i class="fas fa-user fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Hồ sơ cá nhân
                                </a>
                                <a class="dropdown-item" href="<?php echo BASE_URL; ?>/account/orders.php"> <?php // Giữ nguyên link này nếu admin cũng có thể xem đơn hàng của họ ?>
                                    <i class="fas fa-shopping-bag fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Đơn hàng của tôi
                                </a>
                                 <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="<?php echo BASE_URL; ?>/account/change-password.php"> <?php // Link đến trang đổi mật khẩu chung ?>
                                    <i class="fas fa-key fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Đổi mật khẩu
                                </a>
                                <?php
                                // Không cần mục "Quản trị hệ thống" vì đang ở trang admin rồi
                                // if (is_admin()):
                                // endif;
                                ?>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/logout.php">
                                    <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Đăng xuất
                                </a>
                                <?php else: ?>
                                    <a class="dropdown-item" href="#!">Lỗi: Không có thông tin người dùng</a>
                                <?php endif; ?>
                            </div>
                        </li>
                    </ul>
                </nav>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
