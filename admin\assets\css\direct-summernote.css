/* Summernote Direct CSS */

/* <PERSON><PERSON><PERSON> bảo trình soạn thảo hiển thị đúng */
.summernote-editor {
    min-height: 300px;
    border: 1px solid #ddd;
    padding: 10px;
    margin-bottom: 20px;
    background-color: #fff;
}

/* <PERSON><PERSON><PERSON> bảo nội dung soạn thảo hiển thị đúng */
.summernote-editor p {
    margin-bottom: 1em;
}

.summernote-editor h1, .summernote-editor h2, .summernote-editor h3,
.summernote-editor h4, .summernote-editor h5, .summernote-editor h6 {
    margin-top: 1em;
    margin-bottom: 0.5em;
}

/* <PERSON><PERSON><PERSON> bảo hiển thị đúng trên thiết bị di động */
@media (max-width: 768px) {
    .summernote-editor {
        min-height: 200px;
    }
}

/* <PERSON><PERSON> đè các kiểu CSS khác có thể gây xung đột */
.note-editor.note-frame {
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    margin-bottom: 20px !important;
}

.note-editor.note-frame .note-editing-area {
    background-color: #fff !important;
}

.note-editor.note-frame .note-statusbar {
    background-color: #f8f9fa !important;
}

.note-editor.note-frame .note-toolbar {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #ddd !important;
    padding: 8px !important;
}

/* Đảm bảo các nút hiển thị đúng */
.note-btn {
    border-color: #ddd !important;
    background-color: #fff !important;
    padding: 5px 10px !important;
}

.note-btn:hover {
    background-color: #f0f0f0 !important;
}

.note-btn.active {
    background-color: #e6e6e6 !important;
}

/* Đảm bảo dropdown hiển thị đúng */
.note-dropdown-menu {
    min-width: 160px !important;
    padding: 5px 0 !important;
    background-color: #fff !important;
    border: 1px solid rgba(0, 0, 0, 0.15) !important;
    border-radius: 4px !important;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175) !important;
    z-index: 1050 !important;
}

.note-dropdown-item {
    display: block !important;
    padding: 3px 20px !important;
    clear: both !important;
    font-weight: 400 !important;
    line-height: 1.5 !important;
    color: #333 !important;
    white-space: nowrap !important;
}

.note-dropdown-item:hover {
    background-color: #f5f5f5 !important;
    color: #262626 !important;
    text-decoration: none !important;
}

/* Đảm bảo modal hiển thị đúng */
.note-modal-title {
    font-size: 18px !important;
    margin: 0 !important;
    line-height: 1.5 !important;
}

.note-modal-header {
    padding: 15px !important;
    border-bottom: 1px solid #e5e5e5 !important;
}

.note-modal-body {
    position: relative !important;
    padding: 15px !important;
}

.note-modal-footer {
    padding: 15px !important;
    text-align: right !important;
    border-top: 1px solid #e5e5e5 !important;
}

/* Đảm bảo nội dung soạn thảo hiển thị đúng */
.note-editable {
    background-color: #fff !important;
    color: #000 !important;
    padding: 10px !important;
    overflow: auto !important;
    outline: none !important;
    min-height: 300px !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    font-size: 14px !important;
    line-height: 1.6 !important;
}
