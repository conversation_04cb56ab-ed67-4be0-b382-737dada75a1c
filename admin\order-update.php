<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra đăng nhập và quyền admin
if (!is_admin_logged_in() || !check_admin_role()) {
    set_flash_message('error', 'Bạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/admin/login.php');
}

// Kiểm tra ID đơn hàng và trạng thái
if (!isset($_GET['id']) || !is_numeric($_GET['id']) || !isset($_GET['status'])) {
    set_flash_message('error', 'Thông tin không hợp lệ.');
    redirect('orders.php');
}

$order_id = intval($_GET['id']);
$status = $_GET['status'];

// Kiểm tra trạng thái hợp lệ
$valid_statuses = ['pending', 'processing', 'shipping', 'completed', 'cancelled'];
if (!in_array($status, $valid_statuses)) {
    set_flash_message('error', 'Trạng thái không hợp lệ.');
    redirect('orders.php');
}

// Kiểm tra đơn hàng tồn tại
$order = get_order_by_id($order_id);
if (!$order) {
    set_flash_message('error', 'Đơn hàng không tồn tại.');
    redirect('orders.php');
}

// Cập nhật trạng thái đơn hàng
$result = update_order_status($order_id, $status);

// Xử lý kết quả
if ($result['success']) {
    set_flash_message('success', $result['message']);
} else {
    set_flash_message('error', $result['message']);
}

// Chuyển hướng về trang chi tiết đơn hàng
redirect('order-detail.php?id=' . $order_id);
?>
