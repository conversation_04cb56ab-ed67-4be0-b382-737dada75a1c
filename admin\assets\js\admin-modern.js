/**
 * Admin Modern JavaScript - Nội Thất Bàng Vũ
 * JavaScript cho giao diện admin hiện đại
 */

document.addEventListener('DOMContentLoaded', function() {
    // X<PERSON> lý toggle sidebar trên thiết bị di động
    const sidebarToggleTop = document.getElementById('sidebarToggleTop');
    const sidebar = document.getElementById('sidebar');
    const contentWrapper = document.getElementById('content-wrapper');

    if (sidebarToggleTop && sidebar && contentWrapper) {
        sidebarToggleTop.addEventListener('click', function() {
            sidebar.classList.toggle('toggled');
            contentWrapper.classList.toggle('toggled');
        });
    }

    // Đánh dấu menu active dựa trên URL hiện tại
    const currentUrl = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(function(link) {
        const href = link.getAttribute('href');
        if (href && currentUrl.includes(href) && href !== '#') {
            link.closest('.nav-item').classList.add('active');

            // Nếu là submenu, mở dropdown cha (Bootstrap sẽ tự xử lý việc này nếu HTML đúng)
            const parentCollapse = link.closest('.collapse');
            if (parentCollapse && !parentCollapse.classList.contains('show')) {
                // Bootstrap sẽ tự động thêm class 'show' khi parent accordion được mở
                // Không cần can thiệp ở đây nếu cấu trúc HTML và data-attributes đúng
                 const parentToggle = document.querySelector(`[data-target="#${parentCollapse.id}"]`);
                 if (parentToggle && parentToggle.classList.contains('collapsed')) {
                    // parentToggle.click(); // Gây ra vòng lặp nếu Bootstrap cũng xử lý
                 }
            }
        }
    });

    // Xử lý đóng thông báo
    const alertCloseButtons = document.querySelectorAll('.alert .close'); // Sử dụng class chung hơn

    alertCloseButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            const alert = this.closest('.alert');
            if (alert) {
                alert.classList.add('fade-out');
                setTimeout(function() {
                    alert.remove();
                }, 300);
            }
        });
    });

    // Tự động đóng thông báo sau 2 giây (thay đổi từ 5 giây)
    const autoCloseAlerts = document.querySelectorAll('.alert[data-auto-close="true"]');

    autoCloseAlerts.forEach(function(alert) {
        setTimeout(function() {
            alert.classList.add('fade-out');
            setTimeout(function() {
                alert.remove();
            }, 300);
        }, 5000); // Thay đổi về 5000
    });

    // Tự động đóng TẤT CẢ thông báo sau 2 giây (bao gồm cả flash messages)
    // Chỉ xử lý nếu hệ thống unified-notifications chưa sẵn sàng
    setTimeout(function() {
        if (typeof window.showUnifiedNotification !== 'function') {
            const allAlerts = document.querySelectorAll('.alert, .custom-alert');

            allAlerts.forEach(function(alert) {
                // Bỏ qua nếu đã có auto-close hoặc đã được xử lý
                if (alert.hasAttribute('data-auto-close') || alert.hasAttribute('data-auto-hide-processed')) {
                    return;
                }

                // Đánh dấu đã xử lý
                alert.setAttribute('data-auto-hide-processed', 'true');

                setTimeout(function() {
                    if (alert.parentNode) { // Kiểm tra element vẫn tồn tại
                        alert.classList.add('fade-out');
                        setTimeout(function() {
                            if (alert.parentNode) {
                                alert.remove();
                            }
                        }, 300);
                    }
                }, 5000);
            });
        }
    }, 100); // Delay nhỏ để unified-notifications có thể khởi tạo trước

    // Xử lý thanh tìm kiếm
    const searchInput = document.querySelector('.topbar-search-input');

    if (searchInput) {
        searchInput.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        searchInput.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });

        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                // Xử lý tìm kiếm ở đây
                console.log('Searching for:', this.value);
            }
        });
    }

    // Xử lý dropdown menu trong topbar (Bootstrap có thể tự xử lý nếu dùng đúng data attributes)
    const dropdowns = document.querySelectorAll('.topbar .dropdown'); // Chỉ xử lý dropdown trong topbar

    dropdowns.forEach(function(dropdown) {
        const toggle = dropdown.querySelector('[data-toggle="dropdown"]');
        const menu = dropdown.querySelector('.dropdown-menu');

        // Kiểm tra xem Bootstrap đã xử lý chưa bằng cách kiểm tra event listeners của Bootstrap
        // Hoặc đơn giản là không can thiệp nếu Bootstrap đã có sẵn
        if (toggle && menu && !$(toggle).data('bs.dropdown')) { // Kiểm tra nếu không có instance dropdown của Bootstrap
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                // Đóng các menu khác trước khi mở menu hiện tại
                document.querySelectorAll('.topbar .dropdown-menu.show').forEach(openMenu => {
                    if (openMenu !== menu) {
                        openMenu.classList.remove('show');
                    }
                });
                menu.classList.toggle('show');
            });
        }
    });

    // Đóng dropdown topbar khi click bên ngoài
    document.addEventListener('click', function(e) {
        document.querySelectorAll('.topbar .dropdown-menu.show').forEach(openMenu => {
            const dropdownElement = openMenu.closest('.dropdown');
            if (dropdownElement && !dropdownElement.contains(e.target)) {
                openMenu.classList.remove('show');
            }
        });
    });
});
