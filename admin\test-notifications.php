<?php
// Include init
require_once '../includes/init.php';

// Kiểm tra đăng nhập và quyền admin
if (!is_admin_logged_in() || !check_admin_role()) {
    set_flash_message('error', '<PERSON>ạn không có quyền truy cập trang này.');
    redirect(BASE_URL . '/admin/login.php');
}

// Xử lý test thông báo
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_type'])) {
    $test_type = $_POST['test_type'];

    switch ($test_type) {
        case 'success':
            set_flash_message('success', 'Đây là thông báo thành công! Thông báo này sẽ tự động ẩn sau 5 giây.');
            break;
        case 'error':
            set_flash_message('error', '<PERSON><PERSON><PERSON> là thông báo lỗi! Thông báo này sẽ tự động ẩn sau 5 giây.');
            break;
        case 'warning':
            set_flash_message('warning', '<PERSON><PERSON><PERSON> là thông báo cảnh báo! Thông báo này sẽ tự động ẩn sau 5 giây.');
            break;
        case 'info':
            set_flash_message('info', 'Đây là thông báo thông tin! Thông báo này sẽ tự động ẩn sau 5 giây.');
            break;
    }

    redirect($_SERVER['REQUEST_URI']);
}

$page_title = 'Test Thông Báo';
include_once 'partials/header.php';
?>

<!-- Content -->
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Test Hệ Thống Thông Báo</h1>

    <!-- Thông báo -->
    <?php display_flash_message(); ?>

    <!-- Test Buttons -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Test Flash Messages (PHP)</h6>
        </div>
        <div class="card-body">
            <p>Các thông báo này được tạo từ PHP và sẽ tự động ẩn sau 5 giây:</p>
            <form method="POST" style="display: inline;">
                <input type="hidden" name="test_type" value="success">
                <button type="submit" class="btn btn-success mr-2">Test Success</button>
            </form>
            <form method="POST" style="display: inline;">
                <input type="hidden" name="test_type" value="error">
                <button type="submit" class="btn btn-danger mr-2">Test Error</button>
            </form>
            <form method="POST" style="display: inline;">
                <input type="hidden" name="test_type" value="warning">
                <button type="submit" class="btn btn-warning mr-2">Test Warning</button>
            </form>
            <form method="POST" style="display: inline;">
                <input type="hidden" name="test_type" value="info">
                <button type="submit" class="btn btn-info mr-2">Test Info</button>
            </form>
        </div>
    </div>

    <!-- Test JavaScript Notifications -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Test JavaScript Notifications</h6>
        </div>
        <div class="card-body">
            <p>Các thông báo này được tạo từ JavaScript và sẽ tự động ẩn sau 5 giây:</p>
            <button type="button" class="btn btn-success mr-2" onclick="testJSNotification('success')">JS Success</button>
            <button type="button" class="btn btn-danger mr-2" onclick="testJSNotification('danger')">JS Error</button>
            <button type="button" class="btn btn-warning mr-2" onclick="testJSNotification('warning')">JS Warning</button>
            <button type="button" class="btn btn-info mr-2" onclick="testJSNotification('info')">JS Info</button>
        </div>
    </div>

    <!-- Test Product Management Notifications -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Test Product Management Style</h6>
        </div>
        <div class="card-body">
            <p>Các thông báo này mô phỏng thông báo từ product management:</p>
            <button type="button" class="btn btn-success mr-2" onclick="testProductNotification('success')">Product Success</button>
            <button type="button" class="btn btn-danger mr-2" onclick="testProductNotification('error')">Product Error</button>
        </div>
    </div>
</div>

<script>
// Test JavaScript notifications
function testJSNotification(type) {
    const messages = {
        'success': 'Thao tác thành công! (JavaScript)',
        'danger': 'Có lỗi xảy ra! (JavaScript)',
        'warning': 'Cảnh báo! (JavaScript)',
        'info': 'Thông tin! (JavaScript)'
    };

    if (typeof window.showUnifiedNotification === 'function') {
        window.showUnifiedNotification(messages[type], type, 5000);
    } else {
        alert('Hệ thống thông báo thống nhất chưa sẵn sàng!');
    }
}

// Test product management style notifications
function testProductNotification(type) {
    const messages = {
        'success': 'Sản phẩm đã được cập nhật thành công!',
        'error': 'Lỗi khi cập nhật sản phẩm!'
    };

    // Tạo thông báo theo style product management
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const notificationHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert" data-auto-close="true">
            ${messages[type]}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    `;

    // Thêm vào container
    let container = document.querySelector('.notification-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notification-container';
        document.querySelector('.container-fluid').insertBefore(container, document.querySelector('.container-fluid').firstChild);
    }

    container.insertAdjacentHTML('beforeend', notificationHtml);
}

// Log để debug
console.log('Test notifications page loaded');
console.log('Unified notification system available:', typeof window.showUnifiedNotification === 'function');

// Debug function để kiểm tra hệ thống
function debugNotificationSystem() {
    console.log('=== NOTIFICATION SYSTEM DEBUG ===');
    console.log('1. Unified system available:', typeof window.showUnifiedNotification === 'function');

    const notifications = document.querySelectorAll('.alert, .custom-alert, .notification, .flash-message, [class*="alert-"], [role="alert"]');
    console.log('2. Current notifications found:', notifications.length);

    notifications.forEach((notification, index) => {
        console.log(`   Notification ${index + 1}:`, {
            element: notification,
            classes: notification.className,
            hasAutoHideProcessed: notification.hasAttribute('data-auto-hide-processed'),
            hasAutoClose: notification.hasAttribute('data-auto-close'),
            visible: notification.offsetParent !== null
        });
    });

    console.log('3. Notification containers:');
    const containers = document.querySelectorAll('.notification-container, .alert-container');
    containers.forEach((container, index) => {
        console.log(`   Container ${index + 1}:`, container);
    });

    console.log('=== END DEBUG ===');
}

// Chạy debug sau 1 giây
setTimeout(debugNotificationSystem, 1000);

// Thêm nút debug
document.addEventListener('DOMContentLoaded', function() {
    const debugButton = document.createElement('button');
    debugButton.textContent = 'Debug System';
    debugButton.className = 'btn btn-secondary btn-sm';
    debugButton.onclick = debugNotificationSystem;
    debugButton.style.position = 'fixed';
    debugButton.style.top = '10px';
    debugButton.style.right = '10px';
    debugButton.style.zIndex = '9999';
    document.body.appendChild(debugButton);
});
</script>

<?php
include_once 'partials/footer.php';
?>
