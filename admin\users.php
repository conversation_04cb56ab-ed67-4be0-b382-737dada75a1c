<?php
// Include init
require_once '../includes/init.php';

// Ki<PERSON>m tra quyền admin
require_once 'partials/check_admin.php';

// Thiết lập tiêu đề trang
$page_title = 'Quản lý người dùng';

// X<PERSON> lý các hành động
if (isset($_GET['action']) && isset($_GET['id'])) {
    $action = $_GET['action'];
    $user_id = (int)$_GET['id'];

    // Kiểm tra CSRF token
    if (!isset($_GET['csrf_token']) || !check_csrf_token($_GET['csrf_token'])) {
        set_flash_message('error', 'Phiên làm việc đã hết hạn. Vui lòng thử lại.');
        redirect(BASE_URL . '/admin/users.php');
    }

    // Xử lý khóa tài khoản
    if ($action === 'lock') {
        $result = lock_user_account($user_id);

        if ($result['success']) {
            set_flash_message('success', $result['message']);
        } else {
            set_flash_message('error', $result['message']);
        }

        redirect(BASE_URL . '/admin/users.php');
    }

    // Xử lý mở khóa tài khoản
    if ($action === 'unlock') {
        $result = unlock_user_account($user_id);

        if ($result['success']) {
            set_flash_message('success', $result['message']);
        } else {
            set_flash_message('error', $result['message']);
        }

        redirect(BASE_URL . '/admin/users.php');
    }



    // Xử lý xóa tài khoản
    if ($action === 'delete') {
        $result = delete_user($user_id);

        if ($result['success']) {
            set_flash_message('success', $result['message']);
        } else {
            set_flash_message('error', $result['message']);
        }

        redirect(BASE_URL . '/admin/users.php');
    }
}

// Xử lý tìm kiếm và lọc
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status = isset($_GET['status']) && !empty($_GET['status']) ? $_GET['status'] : null;
$role = isset($_GET['role']) && !empty($_GET['role']) ? $_GET['role'] : null;

// Phân trang
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Lấy danh sách người dùng
$users = get_users($limit, $offset, $search, $status, $role);
$total_users = count_users($search, $status, $role);
$total_pages = ceil($total_users / $limit);

// Include header
include_once 'partials/header.php';

// Tạo CSRF token
$csrf_token = generate_csrf_token();
?>

<!-- Content -->
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Quản lý người dùng</h1>

    <!-- Hiển thị thông báo -->
    <?php display_flash_message(); ?>

    <!-- Bộ lọc và tìm kiếm -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Bộ lọc</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="users.php" class="form-inline">
                <div class="form-group mb-2 mr-2">
                    <input type="text" class="form-control" name="search" placeholder="Tìm kiếm theo tên hoặc email..." value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="form-group mb-2 mr-2">
                    <select class="form-control" name="status">
                        <option value="">-- Tất cả trạng thái --</option>
                        <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>Hoạt động</option>
                        <option value="locked" <?php echo $status === 'locked' ? 'selected' : ''; ?>>Bị khóa</option>
                    </select>
                </div>
                <div class="form-group mb-2 mr-2">
                    <select class="form-control" name="role">
                        <option value="">-- Tất cả vai trò --</option>
                        <option value="admin" <?php echo $role === 'admin' ? 'selected' : ''; ?>>Admin</option>
                        <option value="customer" <?php echo $role === 'customer' ? 'selected' : ''; ?>>Khách hàng</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary mb-2">
                    <i class="fas fa-search"></i> Tìm kiếm
                </button>
                <?php if (!empty($search) || $status !== null || $role !== null): ?>
                <a href="<?php echo BASE_URL; ?>/admin/users.php" class="btn btn-secondary mb-2 ml-2">
                    <i class="fas fa-times"></i> Xóa bộ lọc
                </a>
                <?php endif; ?>
            </form>
        </div>
    </div>

    <!-- Danh sách người dùng -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
            <h6 class="m-0 font-weight-bold text-primary">Danh sách người dùng</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th width="5%">ID</th>
                            <th width="10%">Ảnh đại diện</th>
                            <th width="15%">Họ tên</th>
                            <th width="15%">Email</th>
                            <th width="10%">Số điện thoại</th>
                            <th width="10%">Ngày tạo</th>
                            <th width="8%">Vai trò</th>
                            <th width="8%">Trạng thái</th>
                            <th width="8%">Đơn hàng</th>
                            <th width="15%">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (count($users) > 0): ?>
                        <?php foreach ($users as $user): ?>
                        <?php
                            // Đếm số đơn hàng của người dùng
                            $order_count = count_user_orders($user['id']);
                        ?>
                        <tr>
                            <td><?php echo $user['id']; ?></td>
                            <td>
                                <?php if (!empty($user['avatar'])): ?>
                                <img src="<?php echo BASE_URL; ?>/uploads/avatars/<?php echo $user['avatar']; ?>" alt="Avatar" class="img-profile rounded-circle" style="width: 50px; height: 50px; object-fit: cover;">
                                <?php else: ?>
                                <img src="<?php echo BASE_URL; ?>/assets/img/default-avatar.png" alt="Default Avatar" class="img-profile rounded-circle" style="width: 50px; height: 50px; object-fit: cover;">
                                <?php endif; ?>
                            </td>
                            <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                            <td><?php echo !empty($user['phone']) ? htmlspecialchars($user['phone']) : 'Chưa cập nhật'; ?></td>
                            <td><?php echo date('d/m/Y', strtotime($user['created_at'])); ?></td>
                            <td>
                                <?php if ($user['role'] === 'admin'): ?>
                                <span class="badge badge-primary">Admin</span>
                                <?php else: ?>
                                <span class="badge badge-secondary">Khách hàng</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if (isset($user['status']) && $user['status'] === 'locked'): ?>
                                <span class="badge badge-danger">Bị khóa</span>
                                <?php else: ?>
                                <span class="badge badge-success">Hoạt động</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($order_count > 0): ?>
                                <a href="<?php echo BASE_URL; ?>/admin/orders.php?user_id=<?php echo $user['id']; ?>" class="badge badge-info">
                                    <?php echo $order_count; ?> đơn hàng
                                </a>
                                <?php else: ?>
                                <span class="badge badge-secondary">0 đơn hàng</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <a href="<?php echo BASE_URL; ?>/admin/user-detail.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-info" title="Xem chi tiết">
                                    <i class="fas fa-eye"></i>
                                </a>

                                <?php if ($user['role'] !== 'admin'): ?>
                                <?php if (isset($user['status']) && $user['status'] === 'locked'): ?>
                                <a href="<?php echo BASE_URL; ?>/admin/users.php?action=unlock&id=<?php echo $user['id']; ?>&csrf_token=<?php echo $csrf_token; ?>" class="btn btn-sm btn-success" title="Mở khóa tài khoản" onclick="return confirm('Bạn có chắc chắn muốn mở khóa tài khoản này?');">
                                    <i class="fas fa-unlock"></i>
                                </a>
                                <?php else: ?>
                                <a href="<?php echo BASE_URL; ?>/admin/users.php?action=lock&id=<?php echo $user['id']; ?>&csrf_token=<?php echo $csrf_token; ?>" class="btn btn-sm btn-warning" title="Khóa tài khoản" onclick="return confirm('Bạn có chắc chắn muốn khóa tài khoản này?');">
                                    <i class="fas fa-lock"></i>
                                </a>
                                <?php endif; ?>
                                <?php endif; ?>



                                <?php if ($user['role'] !== 'admin'): ?>
                                <a href="<?php echo BASE_URL; ?>/admin/users.php?action=delete&id=<?php echo $user['id']; ?>&csrf_token=<?php echo $csrf_token; ?>" class="btn btn-sm btn-danger" title="Xóa tài khoản" onclick="return confirm('CẢNH BÁO: Bạn có chắc chắn muốn xóa tài khoản này? Hành động này không thể hoàn tác và sẽ xóa tất cả dữ liệu liên quan đến người dùng này.');">
                                    <i class="fas fa-trash"></i>
                                </a>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php else: ?>
                        <tr>
                            <td colspan="10" class="text-center">Không tìm thấy người dùng nào</td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Phân trang -->
            <?php if ($total_pages > 1): ?>
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center mt-4">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo $status !== null ? '&status=' . $status : ''; ?><?php echo $role !== null ? '&role=' . $role : ''; ?>">
                            Trước
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo $status !== null ? '&status=' . $status : ''; ?><?php echo $role !== null ? '&role=' . $role : ''; ?>">
                            <?php echo $i; ?>
                        </a>
                    </li>
                    <?php endfor; ?>

                    <?php if ($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo $status !== null ? '&status=' . $status : ''; ?><?php echo $role !== null ? '&role=' . $role : ''; ?>">
                            Tiếp
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
// Include footer
include_once 'partials/footer.php';
?>
